"""
Shared Prometheus metrics for the skill extractor application.

This module provides centralized metric definitions to prevent duplicate
registrations across different modules.
"""

from prometheus_client import Counter, Histogram, CollectorRegistry, REGISTRY
from typing import Optional

# Global flag to track if metrics have been initialized
_metrics_initialized = False

# Metric instances (will be initialized once)
REQUEST_COUNT: Optional[Counter] = None
REQUEST_DURATION: Optional[Histogram] = None
INGESTION_REQUESTS: Optional[Counter] = None


def initialize_metrics(registry: Optional[CollectorRegistry] = None) -> None:
    """
    Initialize Prometheus metrics.
    
    This function should be called once at application startup to ensure
    metrics are only registered once in the collector registry.
    
    Args:
        registry: Optional custom registry. If None, uses the default REGISTRY.
    """
    global _metrics_initialized, REQUEST_COUNT, REQUEST_DURATION, INGESTION_REQUESTS
    
    if _metrics_initialized:
        return
    
    if registry is None:
        registry = REGISTRY
    
    # Initialize HTTP request metrics
    REQUEST_COUNT = Counter(
        'http_requests_total',
        'Total HTTP requests',
        ['method', 'endpoint', 'status_code'],
        registry=registry
    )
    
    REQUEST_DURATION = Histogram(
        'http_request_duration_seconds',
        'HTTP request duration',
        ['method', 'endpoint'],
        registry=registry
    )
    
    # Initialize ingestion metrics
    INGESTION_REQUESTS = Counter(
        'ingestion_requests_total',
        'Total ingestion requests',
        ['source_type', 'status'],
        registry=registry
    )
    
    _metrics_initialized = True


def get_request_count() -> Counter:
    """Get the HTTP request counter metric."""
    if REQUEST_COUNT is None:
        raise RuntimeError("Metrics not initialized. Call initialize_metrics() first.")
    return REQUEST_COUNT


def get_request_duration() -> Histogram:
    """Get the HTTP request duration histogram metric."""
    if REQUEST_DURATION is None:
        raise RuntimeError("Metrics not initialized. Call initialize_metrics() first.")
    return REQUEST_DURATION


def get_ingestion_requests() -> Counter:
    """Get the ingestion requests counter metric."""
    if INGESTION_REQUESTS is None:
        raise RuntimeError("Metrics not initialized. Call initialize_metrics() first.")
    return INGESTION_REQUESTS


def reset_metrics() -> None:
    """
    Reset metrics initialization state.
    
    This is primarily useful for testing to allow re-initialization
    of metrics with a clean state.
    """
    global _metrics_initialized, REQUEST_COUNT, REQUEST_DURATION, INGESTION_REQUESTS
    
    _metrics_initialized = False
    REQUEST_COUNT = None
    REQUEST_DURATION = None
    INGESTION_REQUESTS = None
