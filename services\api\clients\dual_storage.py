"""
Dual storage service for coordinating between Supabase and Qdrant.

This module provides functionality to store LinkedIn profile data in both
Supabase (volunteer records with skills) and Qdrant (complete profile data with vectors)
while maintaining data consistency and providing rollback capabilities.
"""

import asyncio
from typing import Dict, Any, Optional, List
from uuid import UUID
from datetime import datetime

from common.models import (
    LinkedInProfileData,
    VolunteerRecord,
    LinkedInProfileVector,
    QdrantMetadata,
    MultiVectorMetadata,
    DualStorageResult
)
from common.settings import settings
from common.logging import get_logger
from services.api.clients.database import get_database_client
from services.api.clients.qdrant import get_qdrant_client
from services.api.clients.vector_service import get_vector_service

logger = get_logger(__name__)


class DualStorageService:
    """
    Service for coordinating storage between Supabase and Qdrant.
    
    This service ensures atomic operations where possible and provides
    rollback capabilities when one database operation fails.
    """
    
    def __init__(self):
        """Initialize the dual storage service."""
        self._vector_service = None
        self._initialized = False

    async def initialize(self):
        """Initialize the service and vector generation service."""
        if self._initialized:
            return

        try:
            # Initialize vector generation service
            self._vector_service = await get_vector_service()

            self._initialized = True
            logger.info("Dual storage service initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize dual storage service: {e}")
            raise
    
    def _extract_skills_from_profile(self, profile_data: LinkedInProfileData) -> List[str]:
        """
        Extract skills array from LinkedIn profile data.
        
        Args:
            profile_data: Complete LinkedIn profile data
            
        Returns:
            List of skills
        """
        skills = []
        
        # Add skills from the skills field
        if profile_data.skills:
            skills.extend(profile_data.skills)
        
        # Extract skills from project descriptions
        for project in profile_data.projects:
            if project.skills_used:
                skills.extend(project.skills_used)
        
        # Remove duplicates and empty strings
        skills = list(set(skill.strip() for skill in skills if skill.strip()))
        
        logger.debug(f"Extracted {len(skills)} unique skills from profile")
        return skills
    
    async def _generate_vector(self, profile_data: LinkedInProfileData) -> List[float]:
        """
        Generate combined vector embedding from LinkedIn profile data (legacy method).

        Args:
            profile_data: Complete LinkedIn profile data

        Returns:
            Vector embedding as list of floats
        """
        if not self._initialized:
            await self.initialize()

        try:
            # Use the vector generation service
            vector = await self._vector_service.generate_embedding(profile_data)
            logger.debug(f"Generated combined vector of dimension {len(vector)} from profile data")
            return vector

        except Exception as e:
            logger.error(f"Failed to generate vector embedding: {e}")
            raise

    async def _generate_multi_vectors(self, profile_data: LinkedInProfileData) -> Dict[str, List[float]]:
        """
        Generate multiple vector embeddings for different profile sections.

        Args:
            profile_data: Complete LinkedIn profile data

        Returns:
            Dictionary mapping vector field names to their embeddings
        """
        if not self._initialized:
            await self.initialize()

        try:
            # Use the vector generation service for multi-vector generation
            vectors = await self._vector_service.generate_multi_vector_embeddings(profile_data)
            logger.debug(f"Generated multi-vectors with fields: {list(vectors.keys())}")
            return vectors

        except Exception as e:
            logger.error(f"Failed to generate multi-vector embeddings: {e}")
            raise
    
    async def store_linkedin_profile(
        self,
        volunteer_id: UUID,
        email: str,
        profile_data: LinkedInProfileData,
        source_url: Optional[str] = None
    ) -> DualStorageResult:
        """
        Store LinkedIn profile data in both Supabase and Qdrant using multi-vector architecture.

        Args:
            volunteer_id: Volunteer identifier
            email: Email address
            profile_data: Complete LinkedIn profile data
            source_url: Original LinkedIn profile URL

        Returns:
            DualStorageResult with operation details
        """
        logger.info(
            f"Starting dual storage for volunteer {volunteer_id}",
            email=email,
            source_url=source_url
        )
        
        supabase_success = False
        qdrant_success = False
        error_message = None
        rollback_performed = False
        
        try:
            # Extract skills and languages for Supabase
            skills = self._extract_skills_from_profile(profile_data)
            languages_spoken = profile_data.languages or []

            # Generate multi-vectors for Qdrant
            multi_vectors = await self._generate_multi_vectors(profile_data)

            # Get clients
            db_client = await get_database_client()
            qdrant_client = get_qdrant_client()

            # Step 1: Store in Qdrant first (easier to rollback)
            logger.info("Storing complete profile data with multi-vectors in Qdrant")

            # Prepare Qdrant metadata
            qdrant_metadata = {
                "volunteer_id": str(volunteer_id),
                "email": email,
                "full_name": profile_data.full_name,
                "skills": skills,
                "languages_spoken": languages_spoken,
                "current_company": profile_data.current_company,
                "location": profile_data.location,
                "source_url": source_url,
                "created_at": datetime.utcnow().isoformat(),
                "profile_data": profile_data.dict()  # Store complete profile data
            }

            # Store in Qdrant with multi-vectors
            qdrant_success = await qdrant_client.upsert_point(
                volunteer_id=str(volunteer_id),
                vectors=multi_vectors,
                metadata=qdrant_metadata
            )
            
            if not qdrant_success:
                error_message = "Failed to store profile data in Qdrant"
                logger.error(error_message)
                return DualStorageResult(
                    volunteer_id=volunteer_id,
                    email=email,
                    supabase_success=False,
                    qdrant_success=False,
                    skills_count=len(skills),
                    vector_dimension=1536,  # Multi-vector dimension
                    error_message=error_message,
                    rollback_performed=False
                )

            # Step 2: Store in Supabase
            logger.info("Storing volunteer record with skills and multi-vector metadata in Supabase")

            # Prepare multi-vector metadata for Supabase
            current_time = datetime.utcnow()
            multi_vector_meta_dict = {
                "collection_name": qdrant_client.collection_name,
                "point_id": str(volunteer_id),
                "vector_fields": list(multi_vectors.keys()),
                "vector_dimension": 1536,
                "vector_stored": True,
                "last_sync": current_time.isoformat(),
                "created_at": current_time.isoformat()
            }

            # Store in Supabase using new schema
            # Use separate fields for skills, vector_metadata, and languages_spoken
            try:
                # Convert profile data to dictionary for storage
                if hasattr(profile_data, 'model_dump'):
                    profile_dict = profile_data.model_dump()
                elif hasattr(profile_data, 'dict'):
                    profile_dict = profile_data.dict()
                else:
                    profile_dict = profile_data

                # Store using new database client method
                supabase_success = await db_client.store_volunteer_profile_with_multi_vector_metadata(
                    volunteer_id=volunteer_id,
                    email=email,
                    profile_data=profile_dict,
                    multi_vector_metadata=multi_vector_meta_dict,
                    source_type="linkedin"
                )
            except Exception as e:
                logger.error(f"Failed to store profile data in Supabase: {e}")
                supabase_success = False

            if not supabase_success:
                error_message = "Failed to store volunteer record in Supabase"
                logger.error(error_message)

                # Rollback Qdrant operation
                logger.warning("Performing rollback: removing data from Qdrant")
                rollback_success = await qdrant_client.delete_point(str(volunteer_id))
                rollback_performed = rollback_success

                if not rollback_success:
                    logger.error("Rollback failed: data may be inconsistent between databases")

                return DualStorageResult(
                    volunteer_id=volunteer_id,
                    email=email,
                    supabase_success=False,
                    qdrant_success=True,  # Was successful before rollback
                    skills_count=len(skills),
                    vector_dimension=1536,  # Multi-vector dimension
                    error_message=error_message,
                    rollback_performed=rollback_performed
                )
            
            # Success!
            logger.info(
                f"Successfully stored LinkedIn profile in both databases with multi-vector support",
                volunteer_id=str(volunteer_id),
                email=email,
                skills_count=len(skills),
                languages_count=len(languages_spoken),
                vector_fields=list(multi_vectors.keys()),
                vector_dimension=1536
            )

            return DualStorageResult(
                volunteer_id=volunteer_id,
                email=email,
                supabase_success=True,
                qdrant_success=True,
                skills_count=len(skills),
                vector_dimension=1536,  # Multi-vector dimension
                error_message=None,
                rollback_performed=False
            )
            
        except Exception as e:
            error_message = f"Dual storage operation failed: {e}"
            logger.error(error_message, exc_info=True)
            
            # Attempt rollback if Qdrant was successful
            if qdrant_success and not supabase_success:
                try:
                    qdrant_client = get_qdrant_client()
                    rollback_success = await qdrant_client.delete_point(str(volunteer_id))
                    rollback_performed = rollback_success
                    logger.info(f"Rollback performed: {rollback_performed}")
                except Exception as rollback_error:
                    logger.error(f"Rollback failed: {rollback_error}")
            
            return DualStorageResult(
                volunteer_id=volunteer_id,
                email=email,
                supabase_success=supabase_success,
                qdrant_success=qdrant_success,
                skills_count=0,
                vector_dimension=1536,  # Multi-vector dimension
                error_message=error_message,
                rollback_performed=rollback_performed
            )


# Global service instance
_dual_storage_service = None


async def get_dual_storage_service() -> DualStorageService:
    """
    Get the global dual storage service instance.
    
    Returns:
        DualStorageService instance
    """
    global _dual_storage_service
    
    if _dual_storage_service is None:
        _dual_storage_service = DualStorageService()
        await _dual_storage_service.initialize()
    
    return _dual_storage_service
