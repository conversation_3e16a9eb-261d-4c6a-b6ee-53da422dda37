# Skill Extractor Backend

A pure-backend system for extracting skills and profile information from LinkedIn profiles and résumé PDFs. The system uses FastAPI with integrated processing, Supabase PostgreSQL for structured data storage, and Qdrant for vector-based similarity search.

## 🚀 Features

- **LinkedIn Profile Extraction**: Automated extraction using Browser-Use Python package
- **Resume PDF Processing**: Two approaches - complex structured parsing and simplified vector-only
- **Vector Similarity Search**: Semantic matching using sentence embeddings
- **Real-time Processing**: Synchronous processing within API endpoints
- **JWT Authentication**: Role-based access control with development token support
- **Health Monitoring**: Comprehensive health checks and Prometheus metrics
- **Managed Database**: Supabase PostgreSQL for structured data storage
- **Dual Storage Architecture**: Qdrant for vectors, Supabase for metadata

## 🏗️ Architecture

```
API Service (FastAPI)
├── Ingestion Endpoints (LinkedIn URLs, PDF URLs)
├── Processing Engine (Browser-Use, spaCy NLP)
├── Vector Engine (Sentence-Transformers, Qdrant)
├── Query Endpoints (Profile retrieval, similarity search)
└── Authentication & Monitoring

External Services
├── Supabase PostgreSQL (managed database)
├── Qdrant (vector database)
├── Azure OpenAI (for Browser-Use)
└── AWS S3 (optional, for PDF storage)
```

## 📋 Prerequisites

- Python 3.11+
- Supabase account and project
- Qdrant instance (local or cloud)
- LinkedIn account for profile extraction
- Azure OpenAI account for Browser-Use

## 🔧 Installation

### 1. Clone Repository
```bash
git clone <repository-url>
cd skill-extractor
```

### 2. Create Virtual Environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Download NLP Models
```bash
python -m spacy download en_core_web_trf
```

## ⚙️ Configuration

### 1. Supabase Setup

#### Create Supabase Project
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Create a new project
3. Wait for the project to be ready (usually 2-3 minutes)
4. Go to Settings → Database
5. Copy your connection string

#### Get Connection Details
Your Supabase connection string will look like:
```
postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres
```

#### Optional: Get Project Details
- **Project URL**: `https://[YOUR-PROJECT-REF].supabase.co`
- **Anon Key**: Found in Settings → API

### 2. Environment Configuration

Copy the environment template:
```bash
cp env.template .env
```

Edit `.env` with your actual values:

```bash
# =============================================================================
# DATABASE CONFIGURATION (SUPABASE)
# =============================================================================
# Required: Your Supabase connection string
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres

# Optional: Supabase project details
SUPABASE_URL=https://[YOUR-PROJECT-REF].supabase.co
SUPABASE_ANON_KEY=[YOUR-ANON-KEY]

# =============================================================================
# VECTOR DATABASE (QDRANT)
# =============================================================================
QDRANT_URL=http://localhost:6333
# For Qdrant Cloud:
# QDRANT_URL=https://[YOUR-CLUSTER].qdrant.tech:6333
# QDRANT_API_KEY=[YOUR-API-KEY]

# =============================================================================
# LINKEDIN CREDENTIALS
# =============================================================================
LINKEDIN_EMAIL=[YOUR-LINKEDIN-EMAIL]
LINKEDIN_PASSWORD=[YOUR-LINKEDIN-PASSWORD]

# =============================================================================
# AZURE OPENAI CONFIGURATION
# =============================================================================
AZURE_OPENAI_ENDPOINT=[YOUR-AZURE-OPENAI-ENDPOINT]
AZURE_OPENAI_API_KEY=[YOUR-AZURE-OPENAI-API-KEY]
AZURE_OPENAI_DEPLOYMENT_NAME=[YOUR-DEPLOYMENT-NAME]

# =============================================================================
# SECURITY & AUTHENTICATION
# =============================================================================
JWT_PUBLIC_KEY=[YOUR-JWT-PUBLIC-KEY]
```

### 3. Database Setup

The application will automatically create the required tables when it starts. The tables include:

- `public.volunteers`: Main volunteer profile data
- `public.processing_logs`: Audit trail for processing operations

### 4. Qdrant Setup

#### Local Qdrant (Development)
```bash
# Using Docker
docker run -p 6333:6333 qdrant/qdrant

# Or download binary from https://github.com/qdrant/qdrant/releases
```

#### Qdrant Cloud (Production)
1. Sign up at [Qdrant Cloud](https://cloud.qdrant.io/)
2. Create a cluster
3. Get your cluster URL and API key
4. Update `QDRANT_URL` and `QDRANT_API_KEY` in `.env`

## 🚀 Running the Application

### 1. Start the API Service
```bash
# Using the run script (recommended)
python run.py --reload

# Or using uvicorn directly
python -m uvicorn services.api.main:app --host 0.0.0.0 --port 8000 --reload
```

The server will start on `http://localhost:8000` (or alternative port if 8000 is busy).

### 2. Verify Health
```bash
curl http://localhost:8000/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-06T10:00:00Z",
  "services": {
    "database": {
      "status": "healthy",
      "database_type": "supabase"
    },
    "qdrant": {
      "status": "healthy"
    }
  }
}
```

### 3. Generate Development Token
For testing, generate a JWT token:
```bash
python scripts/generate_test_token.py
```

This will output a token and save it to `dev_token.txt` for easy access.

## 📊 API Usage

### Authentication

All protected endpoints require JWT authentication. Use the generated token:

```bash
# Get your token
TOKEN=$(cat dev_token.txt)

# Use in requests
curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8000/api/volunteer/[ID]
```

### Resume Processing

#### Vector-Only Resume Processing (Recommended)
Simplified approach that works with any resume format:

```bash
curl --location 'http://localhost:8000/api/ingest/resume-simple' \
  --header 'Content-Type: application/json' \
  --header 'Authorization: Bearer YOUR_JWT_TOKEN' \
  --data-raw '{
    "email": "<EMAIL>",
    "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/resume.pdf",
    "filename": "resume.pdf"
  }'
```

**Expected Response:**
```json
{
  "volunteer_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "message": "Resume vector processing completed <NAME_EMAIL>. Processed 3999 characters in 2.01 seconds.",
  "estimated_completion": null
}
```

#### Complex Resume Processing
Structured parsing with multiple vectors:

```bash
curl --location 'http://localhost:8000/api/ingest/resume' \
  --header 'Content-Type: application/json' \
  --header 'Authorization: Bearer YOUR_JWT_TOKEN' \
  --data-raw '{
    "email": "<EMAIL>",
    "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/resume.pdf",
    "filename": "resume.pdf"
  }'
```

### LinkedIn Profile Processing

```bash
curl --location 'http://localhost:8000/api/ingest/linkedin' \
  --header 'Content-Type: application/json' \
  --header 'Authorization: Bearer YOUR_JWT_TOKEN' \
  --data-raw '{
    "url": "https://www.linkedin.com/in/username/",
    "email": "<EMAIL>"
  }'
```

**Expected Response:**
```json
{
  "volunteer_id": "fcbcbc64-f85c-5025-877c-37f4c7a12d6e",
  "status": "processing",
  "message": "LinkedIn profile submitted for processing",
  "estimated_completion": "2025-07-06T11:03:46.764834"
}
```

### Query Endpoints

#### Get Profile by ID
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8000/api/volunteer/[VOLUNTEER-ID]
```

#### Search Similar Profiles
```bash
curl -X POST http://localhost:8000/api/match \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query_text": "Python developer with machine learning experience",
    "limit": 10,
    "threshold": 0.7
  }'
```

#### Get Profile by Email
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8000/api/volunteer/email/<EMAIL>
```

## 🔧 Troubleshooting

### Common Issues

#### 1. "404 Not Found" on API Endpoints
**Problem:** API endpoints return 404 instead of 401 for authentication errors.

**Solution:**
- Ensure the server is running: `python run.py --reload`
- Check the correct endpoint URLs (include `/api/` prefix)
- Verify router configuration in `services/api/main.py`

#### 2. "401 Unauthorized" Errors
**Problem:** Missing or invalid JWT token.

**Solution:**
```bash
# Generate a new development token
python scripts/generate_test_token.py

# Use the token in your requests
curl -H "Authorization: Bearer $(cat dev_token.txt)" \
     http://localhost:8000/api/volunteer/[ID]
```

#### 3. Server Won't Start (Port Issues)
**Problem:** Port 8000 is already in use.

**Solution:**
```bash
# Try alternative ports
python -m uvicorn services.api.main:app --host 0.0.0.0 --port 8080 --reload

# Or kill existing processes
netstat -ano | findstr :8000
taskkill /PID [PID_NUMBER] /F
```

#### 4. Resume Processing Fails
**Problem:** Resume processing returns errors.

**Solution:**
- Use the vector-only endpoint (`/api/ingest/resume-simple`) for better reliability
- Ensure the S3 URL is accessible
- Check environment variables (QDRANT_URL, QDRANT_API_KEY)
- Run direct tests: `python tests/test_resume_endpoints_integration.py`

#### 5. LinkedIn Processing Issues
**Problem:** LinkedIn extraction fails.

**Solution:**
- Configure Azure OpenAI credentials in `.env`
- Set LinkedIn credentials: `LINKEDIN_EMAIL` and `LINKEDIN_PASSWORD`
- Test with: `python tests/test_linkedin_functionality_integration.py`

### Testing Scripts

#### Comprehensive API Testing
```bash
# Test all endpoints with authentication
python scripts/test_api_endpoints.py
```

#### Direct Service Testing
```bash
# Test resume processing directly (bypasses HTTP)
python tests/test_resume_endpoints_integration.py

# Test LinkedIn functionality
python tests/test_linkedin_functionality_integration.py
```

#### Environment Validation
```bash
# Check environment configuration
python run.py --check-env
```

### Development Tips

1. **Use Vector-Only Resume Processing**: More reliable for varying resume formats
2. **Generate Fresh Tokens**: Development tokens expire after 24 hours
3. **Check Logs**: Monitor console output for detailed error information
4. **Test with Known Files**: Use files from `s3://skill-assessment-test/pdfs/` for testing

## 🔍 Monitoring

### Health Checks
- **Health**: `GET /health` - Check if all services are ready
- **Root**: `GET /` - Basic service information
- **Metrics**: `GET /metrics` - Prometheus metrics

### Logs
The application uses structured JSON logging:

```json
{
  "timestamp": "2024-01-01T00:00:00Z",
  "level": "INFO",
  "message": "Processing LinkedIn profile",
  "request_id": "req_123",
  "volunteer_id": "vol_456",
  "email": "<EMAIL>"
}
```

## 🗄️ Database Schema

### Volunteers Table (Supabase)
```sql
CREATE TABLE public.volunteers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    headline TEXT,
    location VARCHAR(255),
    about_summary TEXT,
    current_company VARCHAR(255),
    current_position VARCHAR(255),
    skills TEXT[] DEFAULT '{}',
    vector_metadata JSONB DEFAULT '{}',
    languages_spoken TEXT[] DEFAULT '{}',
    source_types TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Profile Data Structure (JSONB)
```json
{
  "basic_info": {
    "full_name": "John Doe",
    "headline": "Software Engineer",
    "location": "San Francisco, CA"
  },
  "experience": [...],
  "education": [...],
  "skills": ["Python", "Machine Learning", "FastAPI"],
  "certifications": [...],
  "volunteer_work": [...],
  "projects": [...]
}
```

## 🔧 Development

### Running Tests
```bash
pytest tests/ -v
```

### Code Formatting
```bash
black .
isort .
```

### Type Checking
```bash
mypy services/
```

### Pre-commit Hooks
```bash
pre-commit install
pre-commit run --all-files
```

## 🚀 Deployment

### Environment Variables for Production

```bash
# Production settings
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Supabase Production
DATABASE_URL=postgresql://postgres:[PROD-PASSWORD]@db.[PROD-PROJECT-REF].supabase.co:5432/postgres

# Qdrant Cloud
QDRANT_URL=https://[YOUR-CLUSTER].qdrant.tech:6333
QDRANT_API_KEY=[YOUR-API-KEY]

# Security
JWT_PUBLIC_KEY=[PRODUCTION-JWT-PUBLIC-KEY]
RATE_LIMIT_PER_MINUTE=30

# Monitoring
ENABLE_METRICS=true
PROMETHEUS_PORT=9090
```

### Docker Deployment (Optional)

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["uvicorn", "services.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 🛠️ Troubleshooting

### Common Issues

#### Supabase Connection Issues
```bash
# Test connection manually
python -c "
import asyncpg
import asyncio

async def test():
    conn = await asyncpg.connect('YOUR_DATABASE_URL')
    result = await conn.fetchval('SELECT 1')
    print(f'Connection successful: {result}')
    await conn.close()

asyncio.run(test())
"
```

#### Qdrant Connection Issues
```bash
# Test Qdrant connection
curl http://localhost:6333/collections
```

#### Browser-Use Issues
- Ensure LinkedIn credentials are correct
- Check if LinkedIn account has 2FA enabled (may cause issues)
- Verify Azure OpenAI deployment is accessible

### Logs and Debugging

Enable debug logging:
```bash
LOG_LEVEL=DEBUG python services/api/main.py
```

Check Supabase logs in the Supabase Dashboard under Logs.

## ⚡ Quick Reference

### Start Server
```bash
python run.py --reload
```

### Generate Token
```bash
python scripts/generate_test_token.py
```

### Test Resume Processing (Vector-Only)
```bash
curl --location 'http://localhost:8000/api/ingest/resume-simple' \
  --header 'Content-Type: application/json' \
  --header 'Authorization: Bearer $(cat dev_token.txt)' \
  --data-raw '{
    "email": "<EMAIL>",
    "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/test.pdf",
    "filename": "test.pdf"
  }'
```

### Test LinkedIn Processing
```bash
curl --location 'http://localhost:8000/api/ingest/linkedin' \
  --header 'Content-Type: application/json' \
  --header 'Authorization: Bearer $(cat dev_token.txt)' \
  --data-raw '{
    "url": "https://www.linkedin.com/in/username/",
    "email": "<EMAIL>"
  }'
```

### Run Tests
```bash
# Test all endpoints
python scripts/test_api_endpoints.py

# Test resume processing directly
python tests/test_resume_endpoints_integration.py

# Test LinkedIn functionality
python tests/test_linkedin_functionality_integration.py
```

## 📝 API Documentation

Once running, visit:
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the logs for error messages
3. Check Supabase and Qdrant service status
4. Create an issue in the repository 