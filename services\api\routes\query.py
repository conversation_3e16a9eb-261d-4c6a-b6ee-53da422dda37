"""
Query endpoints for the skill extractor API.

This module provides endpoints for retrieving volunteer profiles and performing
vector similarity searches against the Qdrant database.
"""

import time
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, HTTPException, Depends, Query, status
from fastapi.responses import JSONResponse

from common.models import VectorSearchRequest, VectorSearchResponse, VectorSearchResult
from common.settings import settings
from common.logging import get_logger
from services.api.middleware import get_current_user, require_role
from services.api.clients.qdrant import get_qdrant_client, QdrantClient
from services.api.clients.database import get_database_client, DatabaseClient

logger = get_logger(__name__)

router = APIRouter()


@router.get("/volunteer/{volunteer_id}")
async def get_volunteer_profile(
    volunteer_id: UUID,
    include_vector: bool = Query(False, description="Include vector data in response"),
    current_user: dict = Depends(get_current_user),
    db_client: DatabaseClient = Depends(get_database_client),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Retrieve a complete volunteer profile by ID using dual storage lookup.

    This endpoint returns merged data from Supabase (volunteer record with skills)
    and optionally from Qdrant (complete profile data with vector embeddings).
    """

    logger.info(
        "Volunteer profile requested with dual storage lookup",
        volunteer_id=str(volunteer_id),
        include_vector=include_vector,
        user_id=current_user.get("sub")
    )

    try:
        # Get volunteer data with optional Qdrant lookup
        volunteer_data = await db_client.get_volunteer_with_qdrant_lookup(
            volunteer_id=volunteer_id,
            include_vector_data=include_vector
        )

        if not volunteer_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "Volunteer not found",
                    "message": f"No volunteer found with ID {volunteer_id}",
                    "volunteer_id": str(volunteer_id)
                }
            )

        # Extract skills from the new schema (direct array field)
        skills_list = volunteer_data.get("skills", [])
        if not isinstance(skills_list, list):
            skills_list = []

        # Extract languages from the new schema
        languages_spoken = volunteer_data.get("languages_spoken", [])
        if not isinstance(languages_spoken, list):
            languages_spoken = []

        # Extract vector metadata from the new schema
        vector_metadata = volunteer_data.get("vector_metadata", {})

        response_data = {
            "volunteer_id": volunteer_id,
            "profile": {
                "basic_info": {
                    "full_name": volunteer_data.get("full_name"),
                    "email": volunteer_data.get("email"),
                    "headline": volunteer_data.get("headline"),
                    "location": volunteer_data.get("location"),
                    "about_summary": volunteer_data.get("about_summary")
                },
                "current_role": {
                    "company": volunteer_data.get("current_company"),
                    "position": volunteer_data.get("current_position")
                },
                "skills": skills_list,
                "skills_count": len(skills_list),
                "languages_spoken": languages_spoken,
                "languages_count": len(languages_spoken)
            },
            "vector_metadata": vector_metadata,
            "last_updated": volunteer_data.get("updated_at"),
            "source_types": volunteer_data.get("source_types", [])
        }

        # Include vector data if requested and available
        if include_vector and volunteer_data.get("vector_data"):
            vector_data = volunteer_data["vector_data"]
            vectors = vector_data.get("vectors", {})

            # Handle both single vector (legacy) and multi-vector formats
            if isinstance(vectors, dict) and vectors:
                # Multi-vector format
                response_data["vectors"] = {
                    "embeddings": vectors,
                    "metadata": vector_data.get("payload", {}),
                    "vector_dimension": 1536,
                    "vector_fields": list(vectors.keys()),
                    "collection_name": vector_metadata.get("collection_name")
                }
            elif vector_data.get("vector"):
                # Legacy single vector format
                response_data["vectors"] = {
                    "embeddings": {"combined_vector": vector_data.get("vector")},
                    "metadata": vector_data.get("payload", {}),
                    "vector_dimension": len(vector_data.get("vector", [])),
                    "vector_fields": ["combined_vector"],
                    "collection_name": vector_metadata.get("collection_name")
                }
            else:
                response_data["vectors"] = None
                response_data["vector_note"] = "Vector data format not recognized"
        elif include_vector:
            response_data["vectors"] = None
            response_data["vector_note"] = "Vector data not available or not stored"

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to retrieve volunteer profile",
            volunteer_id=str(volunteer_id),
            error=str(e),
            exc_info=True
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Profile retrieval failed",
                "message": "Failed to retrieve volunteer profile",
                "volunteer_id": str(volunteer_id)
            }
        )


@router.get("/volunteer/email/{email}")
async def get_volunteer_by_email(
    email: str,
    include_vector: bool = Query(False, description="Include vector data in response"),
    current_user: dict = Depends(get_current_user),
    db_client: DatabaseClient = Depends(get_database_client),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Retrieve a volunteer profile by email using dual storage lookup.

    This endpoint finds a volunteer by email and returns merged data from both databases.
    """

    logger.info(
        "Volunteer profile requested by email",
        email=email,
        include_vector=include_vector,
        user_id=current_user.get("sub")
    )

    try:
        # Get volunteer data by email
        volunteer_data = await db_client.get_volunteer_by_email(email)

        if not volunteer_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "Volunteer not found",
                    "message": f"No volunteer found with email {email}",
                    "email": email
                }
            )

        volunteer_id = UUID(volunteer_data["id"])

        # Use the existing profile endpoint logic
        return await get_volunteer_profile(
            volunteer_id=volunteer_id,
            include_vector=include_vector,
            current_user=current_user,
            db_client=db_client,
            qdrant_client=qdrant_client
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to retrieve volunteer profile by email",
            email=email,
            error=str(e),
            exc_info=True
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Profile retrieval failed",
                "message": "Failed to retrieve volunteer profile by email",
                "email": email
            }
        )


@router.get("/vector/{volunteer_id}")
async def get_volunteer_vector(
    volunteer_id: UUID,
    current_user: dict = Depends(get_current_user),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Retrieve vector representation for a specific volunteer.
    
    This endpoint returns the vector embedding and associated metadata
    from the Qdrant vector database.
    """
    
    logger.info(
        "Vector data requested",
        volunteer_id=str(volunteer_id),
        user_id=current_user.get("sub")
    )
    
    try:
        vector_data = await qdrant_client.get_point(str(volunteer_id))
        
        if not vector_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "Vector not found",
                    "message": f"No vector found for volunteer {volunteer_id}",
                    "volunteer_id": str(volunteer_id)
                }
            )
        
        response = {
            "volunteer_id": volunteer_id,
            "vector": vector_data.get("vector"),
            "metadata": vector_data.get("payload", {}),
            "vector_size": len(vector_data.get("vector", [])),
            "last_updated": vector_data.get("payload", {}).get("last_updated")
        }
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to retrieve vector data",
            volunteer_id=str(volunteer_id),
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Vector retrieval failed",
                "message": "Failed to retrieve vector data",
                "volunteer_id": str(volunteer_id)
            }
        )


@router.post("/match", response_model=VectorSearchResponse)
async def vector_similarity_search(
    request: VectorSearchRequest,
    current_user: dict = Depends(get_current_user),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Perform vector similarity search to find matching volunteers.
    
    This endpoint supports multiple search modes:
    1. Vector-based search: Provide a query vector directly
    2. Text-based search: Provide text that will be vectorized  
    3. Volunteer-based search: Use another volunteer's vector as the query
    """
    
    start_time = time.time()
    
    logger.info(
        "Vector similarity search requested",
        has_query_vector=request.query_vector is not None,
        has_query_text=request.query_text is not None,
        reference_volunteer_id=str(request.volunteer_id) if request.volunteer_id else None,
        top_k=request.top_k,
        user_id=current_user.get("sub")
    )
    
    try:
        # Determine query vector
        query_vector = None

        if request.query_vector:
            query_vector = request.query_vector
        elif request.query_text:
            # Generate vector from query text
            from services.api.clients.vector_service import get_vector_service
            vector_service = await get_vector_service()
            query_vector = await vector_service.generate_embedding_from_text(request.query_text)

            logger.info(
                "Generated query vector from text",
                query_text=request.query_text,
                vector_dimension=len(query_vector)
            )
        elif request.volunteer_id:
            # Use another volunteer's vector as the query
            reference_vector_data = await qdrant_client.get_point(str(request.volunteer_id))
            if not reference_vector_data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "error": "Reference volunteer not found",
                        "message": f"No vector found for volunteer {request.volunteer_id}"
                    }
                )
            query_vector = reference_vector_data.get("vector")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "Invalid search request",
                    "message": "Must provide query_vector, query_text, or volunteer_id"
                }
            )
        
        # Perform multi-vector search
        search_results = await qdrant_client.search_similar(
            query_vector=query_vector,
            vector_field=request.vector_field,
            top_k=request.top_k,
            score_threshold=request.score_threshold,
            filters=request.filters
        )
        
        # Convert results
        enriched_results = []
        for result in search_results:
            enriched_result = VectorSearchResult(
                volunteer_id=UUID(result["id"]),
                score=result["score"],
                metadata=result.get("payload", {})
            )
            enriched_results.append(enriched_result)
        
        query_time_ms = (time.time() - start_time) * 1000
        
        response = VectorSearchResponse(
            results=enriched_results,
            total_results=len(enriched_results),
            query_time_ms=query_time_ms,
            vector_field=request.vector_field,
            vector_dimension=1536
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Vector similarity search failed",
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Search failed",
                "message": "Failed to perform vector similarity search"
            }
        )


@router.get("/search/similar/{volunteer_id}")
async def find_similar_volunteers(
    volunteer_id: UUID,
    vector_field: str = Query("combined_vector", description="Vector field to use for similarity search"),
    top_k: int = Query(10, ge=1, le=100, description="Number of similar volunteers to return"),
    score_threshold: Optional[float] = Query(None, ge=0.0, le=1.0, description="Minimum similarity score"),
    current_user: dict = Depends(get_current_user),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Find volunteers similar to a given volunteer.
    
    This is a convenience endpoint that uses an existing volunteer's vector
    to find other similar volunteers in the database.
    """
    
    logger.info(
        "Similar volunteers search requested",
        reference_volunteer_id=str(volunteer_id),
        vector_field=vector_field,
        top_k=top_k,
        score_threshold=score_threshold,
        user_id=current_user.get("sub")
    )

    # Create a search request and delegate to the main search endpoint
    search_request = VectorSearchRequest(
        volunteer_id=volunteer_id,
        vector_field=vector_field,
        top_k=top_k,
        score_threshold=score_threshold
    )
    
    return await vector_similarity_search(
        request=search_request,
        current_user=current_user,
        qdrant_client=qdrant_client
    )


@router.post("/resumes/search", response_model=VectorSearchResponse)
async def search_resumes(
    query_text: str,
    vector_field: str = "combined_vector",
    top_k: int = Query(10, ge=1, le=100, description="Number of results to return"),
    score_threshold: Optional[float] = Query(0.5, ge=0.0, le=1.0, description="Minimum similarity score"),
    current_user: dict = Depends(get_current_user)
):
    """
    Search resumes using natural language queries.

    This endpoint is specifically designed for searching resume data stored in Qdrant.
    It converts text queries to vectors and searches against the resumes collection.

    Examples:
    - "marketing experience"
    - "Python developer with machine learning"
    - "project manager with agile experience"
    """

    start_time = time.time()

    logger.info(
        "Resume search requested",
        query_text=query_text,
        vector_field=vector_field,
        top_k=top_k,
        score_threshold=score_threshold,
        user_id=current_user.get("sub")
    )

    try:
        # Get resumes-specific Qdrant client
        from services.api.clients.qdrant import get_qdrant_resumes_client
        resumes_qdrant_client = get_qdrant_resumes_client()

        # Generate vector from query text
        from services.api.clients.vector_service import get_vector_service
        vector_service = await get_vector_service()
        query_vector = await vector_service.generate_embedding_from_text(query_text)

        logger.info(
            "Generated query vector for resume search",
            query_text=query_text,
            vector_dimension=len(query_vector)
        )

        # Search in resumes collection
        search_results = await resumes_qdrant_client.search_similar(
            query_vector=query_vector,
            vector_field=vector_field,
            top_k=top_k,
            score_threshold=score_threshold,
            filters={"processing_approach": "vector_only"}  # Filter for vector-only resumes
        )

        # Convert results
        enriched_results = []
        for result in search_results:
            # For resumes, the ID is the point ID, not necessarily a volunteer_id
            # We'll use the email from metadata as the identifier
            metadata = result.get("payload", {})
            email = metadata.get("email", "unknown")

            enriched_result = VectorSearchResult(
                volunteer_id=UUID(result["id"]),  # Using point ID as volunteer_id for compatibility
                score=result["score"],
                metadata={
                    **metadata,
                    "search_type": "resume",
                    "query_text": query_text
                }
            )
            enriched_results.append(enriched_result)

        query_time_ms = (time.time() - start_time) * 1000

        response = VectorSearchResponse(
            results=enriched_results,
            total_results=len(enriched_results),
            query_time_ms=query_time_ms,
            vector_field=vector_field,
            vector_dimension=1536
        )

        logger.info(
            "Resume search completed",
            query_text=query_text,
            results_count=len(enriched_results),
            query_time_ms=query_time_ms
        )

        return response

    except Exception as e:
        logger.error(f"Resume search failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Resume search failed",
                "message": str(e)
            }
        )


@router.get("/stats")
async def get_database_statistics(
    current_user: dict = Depends(require_role("admin")),
    db_client: DatabaseClient = Depends(get_database_client),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Get database statistics and metrics.
    
    This endpoint provides information about the current state of both
    the PostgreSQL database and Qdrant vector database.
    
    Requires 'admin' role for authentication.
    """
    
    logger.info(
        "Database statistics requested",
        user_id=current_user.get("sub")
    )
    
    try:
        # Get Qdrant statistics
        qdrant_stats = await qdrant_client.get_collection_info()
        
        return {
            "timestamp": time.time(),
            "qdrant": qdrant_stats,
            "system_health": "healthy"
        }
        
    except Exception as e:
        logger.error(
            "Failed to get database statistics",
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Statistics unavailable",
                "message": "Failed to retrieve database statistics"
            }
        ) 