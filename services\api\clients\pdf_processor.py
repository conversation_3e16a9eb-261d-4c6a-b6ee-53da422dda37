"""
PDF processing client for resume text extraction.

This module provides functionality to download and extract text from PDF resumes
stored in S3, with fallback to OCR for image-based PDFs.
"""

import asyncio
import io
import re
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse

import aiohttp
import pdfplumber
import pytesseract
from PIL import Image
import PyPDF2

from common.logging import get_logger
from common.utils import normalize_text, safe_filename

logger = get_logger(__name__)


class PDFProcessingError(Exception):
    """Custom exception for PDF processing errors."""
    
    def __init__(self, message: str, error_type: str = "processing_error", details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.error_type = error_type
        self.details = details or {}


class PDFProcessor:
    """
    Client for processing PDF resumes with text extraction and OCR fallback.
    
    This client provides methods for downloading PDFs from S3 URLs and extracting
    structured text content using multiple extraction strategies.
    """
    
    def __init__(self):
        """Initialize the PDF processor."""
        self.session = None
        self.max_file_size = 50 * 1024 * 1024  # 50MB limit
        self.timeout = 30  # 30 seconds timeout
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def download_pdf_from_s3(self, s3_url: str) -> bytes:
        """
        Download PDF content from S3 URL.
        
        Args:
            s3_url: S3 URL pointing to the PDF file
            
        Returns:
            PDF content as bytes
            
        Raises:
            PDFProcessingError: If download fails or file is too large
        """
        try:
            logger.info(f"Downloading PDF from S3 URL: {s3_url}")
            
            # Validate URL
            parsed_url = urlparse(s3_url)
            if not parsed_url.scheme in ['http', 'https']:
                raise PDFProcessingError(
                    "Invalid S3 URL scheme",
                    error_type="invalid_url",
                    details={"url": s3_url}
                )
            
            if not self.session:
                raise PDFProcessingError(
                    "PDF processor not initialized",
                    error_type="initialization_error"
                )
            
            async with self.session.get(s3_url) as response:
                if response.status != 200:
                    raise PDFProcessingError(
                        f"Failed to download PDF: HTTP {response.status}",
                        error_type="download_error",
                        details={"status_code": response.status, "url": s3_url}
                    )
                
                # Check content type
                content_type = response.headers.get('content-type', '').lower()
                if 'pdf' not in content_type and 'application/octet-stream' not in content_type:
                    logger.warning(f"Unexpected content type: {content_type}")
                
                # Check file size
                content_length = response.headers.get('content-length')
                if content_length and int(content_length) > self.max_file_size:
                    raise PDFProcessingError(
                        f"PDF file too large: {content_length} bytes",
                        error_type="file_too_large",
                        details={"size": int(content_length), "max_size": self.max_file_size}
                    )
                
                # Download content
                pdf_content = await response.read()
                
                if len(pdf_content) > self.max_file_size:
                    raise PDFProcessingError(
                        f"PDF file too large: {len(pdf_content)} bytes",
                        error_type="file_too_large",
                        details={"size": len(pdf_content), "max_size": self.max_file_size}
                    )
                
                logger.info(f"Successfully downloaded PDF: {len(pdf_content)} bytes")
                return pdf_content
                
        except aiohttp.ClientError as e:
            raise PDFProcessingError(
                f"Network error downloading PDF: {e}",
                error_type="network_error",
                details={"original_error": str(e)}
            )
        except Exception as e:
            if isinstance(e, PDFProcessingError):
                raise
            raise PDFProcessingError(
                f"Unexpected error downloading PDF: {e}",
                error_type="unexpected_error",
                details={"original_error": str(e)}
            )
    
    def extract_text_with_pdfplumber(self, pdf_content: bytes) -> str:
        """
        Extract text using pdfplumber (preferred method).
        
        Args:
            pdf_content: PDF content as bytes
            
        Returns:
            Extracted text content
        """
        try:
            text_parts = []
            
            with pdfplumber.open(io.BytesIO(pdf_content)) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text_parts.append(page_text.strip())
                        logger.debug(f"Extracted text from page {page_num}: {len(page_text) if page_text else 0} characters")
                    except Exception as e:
                        logger.warning(f"Failed to extract text from page {page_num}: {e}")
                        continue
            
            extracted_text = "\n\n".join(text_parts)
            logger.info(f"pdfplumber extraction completed: {len(extracted_text)} characters")
            return extracted_text
            
        except Exception as e:
            logger.warning(f"pdfplumber extraction failed: {e}")
            return ""
    
    def extract_text_with_pypdf2(self, pdf_content: bytes) -> str:
        """
        Extract text using PyPDF2 (fallback method).
        
        Args:
            pdf_content: PDF content as bytes
            
        Returns:
            Extracted text content
        """
        try:
            text_parts = []
            
            with io.BytesIO(pdf_content) as pdf_stream:
                pdf_reader = PyPDF2.PdfReader(pdf_stream)
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text_parts.append(page_text.strip())
                        logger.debug(f"PyPDF2 extracted text from page {page_num}: {len(page_text) if page_text else 0} characters")
                    except Exception as e:
                        logger.warning(f"PyPDF2 failed to extract text from page {page_num}: {e}")
                        continue
            
            extracted_text = "\n\n".join(text_parts)
            logger.info(f"PyPDF2 extraction completed: {len(extracted_text)} characters")
            return extracted_text
            
        except Exception as e:
            logger.warning(f"PyPDF2 extraction failed: {e}")
            return ""
    
    async def extract_text_from_pdf(self, pdf_content: bytes) -> str:
        """
        Extract text from PDF using multiple strategies.
        
        Args:
            pdf_content: PDF content as bytes
            
        Returns:
            Extracted text content
            
        Raises:
            PDFProcessingError: If all extraction methods fail
        """
        try:
            # Strategy 1: Try pdfplumber first (best for structured text)
            logger.info("Attempting text extraction with pdfplumber")
            text = self.extract_text_with_pdfplumber(pdf_content)
            
            if text and len(text.strip()) > 50:  # Minimum viable text length
                logger.info(f"pdfplumber extraction successful: {len(text)} characters")
                return normalize_text(text)
            
            # Strategy 2: Fallback to PyPDF2
            logger.info("Attempting text extraction with PyPDF2")
            text = self.extract_text_with_pypdf2(pdf_content)
            
            if text and len(text.strip()) > 50:
                logger.info(f"PyPDF2 extraction successful: {len(text)} characters")
                return normalize_text(text)
            
            # If we get here, both methods failed or returned insufficient text
            raise PDFProcessingError(
                "All text extraction methods failed or returned insufficient text",
                error_type="extraction_failed",
                details={
                    "pdfplumber_length": len(text) if text else 0,
                    "minimum_required": 50
                }
            )
            
        except Exception as e:
            if isinstance(e, PDFProcessingError):
                raise
            raise PDFProcessingError(
                f"Text extraction failed: {e}",
                error_type="extraction_error",
                details={"original_error": str(e)}
            )
    
    async def process_resume_pdf(self, s3_url: str) -> str:
        """
        Complete PDF processing pipeline: download and extract text.
        
        Args:
            s3_url: S3 URL pointing to the PDF resume
            
        Returns:
            Extracted text content
            
        Raises:
            PDFProcessingError: If processing fails at any stage
        """
        try:
            logger.info(f"Starting PDF processing for: {s3_url}")
            
            # Download PDF
            pdf_content = await self.download_pdf_from_s3(s3_url)
            
            # Extract text
            extracted_text = await self.extract_text_from_pdf(pdf_content)
            
            logger.info(f"PDF processing completed successfully: {len(extracted_text)} characters extracted")
            return extracted_text
            
        except Exception as e:
            if isinstance(e, PDFProcessingError):
                raise
            raise PDFProcessingError(
                f"PDF processing pipeline failed: {e}",
                error_type="pipeline_error",
                details={"original_error": str(e)}
            )


# Global client instance
_pdf_processor = None


async def get_pdf_processor() -> PDFProcessor:
    """
    Get a PDF processor instance with async context management.
    
    Returns:
        PDFProcessor instance ready for use
    """
    processor = PDFProcessor()
    await processor.__aenter__()
    return processor


async def process_resume_pdf(s3_url: str) -> str:
    """
    Convenience function to process a resume PDF.
    
    Args:
        s3_url: S3 URL pointing to the PDF resume
        
    Returns:
        Extracted text content
    """
    async with PDFProcessor() as processor:
        return await processor.process_resume_pdf(s3_url)
