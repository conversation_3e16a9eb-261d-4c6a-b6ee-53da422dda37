#!/usr/bin/env python3
"""
Simple JWT token generator for development and testing.

This script generates JWT tokens that can be used to test the WeDoGood API endpoints
that require authentication.
"""

import os
import sys
import time
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import jwt
from datetime import datetime, timed<PERSON><PERSON>


def generate_development_token(
    user_id: str = "test-user-123",
    roles: list = None,
    expiry_hours: int = 24
) -> str:
    """
    Generate a development JWT token.
    
    Args:
        user_id: User identifier
        roles: List of user roles
        expiry_hours: Token expiry time in hours
        
    Returns:
        JWT token string
    """
    if roles is None:
        roles = ["service_extract"]
    
    # Create payload
    payload = {
        "sub": user_id,
        "roles": roles,
        "iat": int(time.time()),
        "exp": int(time.time()) + (expiry_hours * 3600),
        "iss": "wedogood-dev",
        "aud": "wedogood-api"
    }
    
    # Use simple secret for development
    secret = os.getenv("JWT_SECRET", "development-secret-key-not-for-production")
    algorithm = "HS256"
    
    # Generate token
    token = jwt.encode(payload, secret, algorithm=algorithm)
    
    return token


def print_token_info(token: str):
    """Print token information."""
    try:
        # Decode without verification to show payload
        payload = jwt.decode(token, options={"verify_signature": False})
        
        print("JWT Token Generated Successfully!")
        print("=" * 60)
        print(f"Token: {token}")
        print()
        print("Token Details:")
        print(f"  User ID: {payload.get('sub')}")
        print(f"  Roles: {', '.join(payload.get('roles', []))}")
        print(f"  Issued At: {datetime.fromtimestamp(payload.get('iat', 0))}")
        print(f"  Expires At: {datetime.fromtimestamp(payload.get('exp', 0))}")
        print(f"  Issuer: {payload.get('iss', 'N/A')}")
        print(f"  Audience: {payload.get('aud', 'N/A')}")
        print()
        print("Usage in curl commands:")
        print(f"  --header 'Authorization: Bearer {token}'")
        print()
        print("Note: This is a development token using HS256 with a simple secret.")
        print("For production, use RS256 with proper key management.")
        
    except Exception as e:
        print(f"Error decoding token: {e}")


def main():
    """Main function."""
    print("WeDoGood Development JWT Token Generator")
    print("=" * 60)
    
    # Check for command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print("Usage:")
            print("  python scripts/generate_test_token.py [user_id] [roles] [expiry_hours]")
            print()
            print("Examples:")
            print("  python scripts/generate_test_token.py")
            print("  python scripts/generate_test_token.py test-user-456")
            print("  python scripts/generate_test_token.py admin-user admin,service_extract")
            print("  python scripts/generate_test_token.py test-user service_extract 48")
            return
    
    # Parse arguments
    user_id = sys.argv[1] if len(sys.argv) > 1 else "test-user-123"
    roles_str = sys.argv[2] if len(sys.argv) > 2 else "service_extract"
    expiry_hours = int(sys.argv[3]) if len(sys.argv) > 3 else 24
    
    # Parse roles
    roles = [role.strip() for role in roles_str.split(",")]
    
    print(f"Generating token for:")
    print(f"  User ID: {user_id}")
    print(f"  Roles: {', '.join(roles)}")
    print(f"  Expiry: {expiry_hours} hours")
    print()
    
    try:
        # Generate token
        token = generate_development_token(user_id, roles, expiry_hours)
        
        # Print token information
        print_token_info(token)
        
        # Save to file for easy access
        token_file = project_root / "dev_token.txt"
        with open(token_file, "w") as f:
            f.write(token)
        
        print(f"Token saved to: {token_file}")
        print("You can also read it with: cat dev_token.txt")
        
    except Exception as e:
        print(f"❌ Error generating token: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
