#!/usr/bin/env python3
"""
Comprehensive API diagnostic script for WeDoGood resume processing.

This script helps diagnose and troubleshoot API issues including:
- Authentication problems
- API endpoint availability
- Resume processing functionality
- LinkedIn processing functionality
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional

import httpx
import jwt
from datetime import datetime, timedelta

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from common.logging import get_logger

logger = get_logger(__name__)


class APIDiagnosticTool:
    """Comprehensive API diagnostic and testing tool."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_results = {}
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def print_header(self):
        """Print diagnostic header."""
        print("=" * 80)
        print("🔍 WEDOGOOD API DIAGNOSTIC TOOL")
        print("=" * 80)
        print(f"Base URL: {self.base_url}")
        print(f"Timestamp: {datetime.now().isoformat()}")
        print()
    
    async def test_basic_connectivity(self) -> bool:
        """Test basic API connectivity."""
        print("📡 Testing Basic API Connectivity...")
        
        try:
            response = await self.client.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                print("✅ API is reachable and responding")
                health_data = response.json()
                print(f"   Status: {health_data.get('status', 'unknown')}")
                return True
            else:
                print(f"❌ API returned status code: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to connect to API: {e}")
            return False
    
    async def test_authentication_requirements(self) -> Dict[str, Any]:
        """Test authentication requirements for protected endpoints."""
        print("\n🔐 Testing Authentication Requirements...")
        
        endpoints_to_test = [
            "/api/ingest/resume",
            "/api/ingest/resume-simple",
            "/api/ingest/linkedin"
        ]
        
        auth_results = {}
        
        for endpoint in endpoints_to_test:
            print(f"   Testing {endpoint}...")
            
            try:
                # Test without authentication
                response = await self.client.post(
                    f"{self.base_url}{endpoint}",
                    json={"test": "data"}
                )
                
                auth_results[endpoint] = {
                    "status_code": response.status_code,
                    "requires_auth": response.status_code in [401, 403],
                    "response": response.text[:200] if response.text else None
                }
                
                if response.status_code == 401:
                    print(f"   ✅ {endpoint} correctly requires authentication (401)")
                elif response.status_code == 403:
                    print(f"   ✅ {endpoint} correctly requires authorization (403)")
                else:
                    print(f"   ⚠️  {endpoint} returned unexpected status: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error testing {endpoint}: {e}")
                auth_results[endpoint] = {"error": str(e)}
        
        return auth_results
    
    def generate_test_jwt(self) -> Optional[str]:
        """Generate a test JWT token for development."""
        print("\n🎫 Attempting to Generate Test JWT...")
        
        # Check if JWT keys are available
        jwt_private_key = os.getenv('JWT_PRIVATE_KEY')
        jwt_algorithm = os.getenv('JWT_ALGORITHM', 'RS256')
        jwt_audience = os.getenv('JWT_AUDIENCE')
        jwt_issuer = os.getenv('JWT_ISSUER')
        
        if not jwt_private_key:
            print("   ❌ JWT_PRIVATE_KEY not found in environment")
            print("   💡 For development, you need to set up JWT keys")
            return None
        
        try:
            # Create test payload
            payload = {
                "sub": "test-user-123",
                "roles": ["service_extract"],
                "iat": datetime.utcnow(),
                "exp": datetime.utcnow() + timedelta(hours=1)
            }
            
            if jwt_audience:
                payload["aud"] = jwt_audience
            if jwt_issuer:
                payload["iss"] = jwt_issuer
            
            # Generate token
            token = jwt.encode(payload, jwt_private_key, algorithm=jwt_algorithm)
            print("   ✅ Test JWT token generated successfully")
            return token
            
        except Exception as e:
            print(f"   ❌ Failed to generate JWT token: {e}")
            return None
    
    async def test_resume_endpoint_with_auth(self, token: Optional[str] = None) -> Dict[str, Any]:
        """Test resume endpoints with authentication."""
        print("\n📄 Testing Resume Endpoints with Authentication...")
        
        if not token:
            print("   ⚠️  No authentication token available")
            return {"error": "no_token"}
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        test_data = {
            "email": "<EMAIL>",
            "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/test.pdf",
            "filename": "test.pdf"
        }
        
        results = {}
        
        # Test original resume endpoint
        print("   Testing /api/ingest/resume...")
        try:
            response = await self.client.post(
                f"{self.base_url}/api/ingest/resume",
                json=test_data,
                headers=headers
            )
            
            results["resume"] = {
                "status_code": response.status_code,
                "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text,
                "success": response.status_code == 200
            }
            
            if response.status_code == 200:
                print("   ✅ Original resume endpoint working")
            else:
                print(f"   ❌ Original resume endpoint failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error testing original resume endpoint: {e}")
            results["resume"] = {"error": str(e)}
        
        # Test vector-only resume endpoint
        print("   Testing /api/ingest/resume-simple...")
        try:
            response = await self.client.post(
                f"{self.base_url}/api/ingest/resume-simple",
                json=test_data,
                headers=headers
            )
            
            results["resume_simple"] = {
                "status_code": response.status_code,
                "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text,
                "success": response.status_code == 200
            }
            
            if response.status_code == 200:
                print("   ✅ Vector-only resume endpoint working")
            else:
                print(f"   ❌ Vector-only resume endpoint failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error testing vector-only resume endpoint: {e}")
            results["resume_simple"] = {"error": str(e)}
        
        return results
    
    async def test_linkedin_endpoint_with_auth(self, token: Optional[str] = None) -> Dict[str, Any]:
        """Test LinkedIn endpoint with authentication."""
        print("\n🔗 Testing LinkedIn Endpoint with Authentication...")
        
        if not token:
            print("   ⚠️  No authentication token available")
            return {"error": "no_token"}
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        test_data = {
            "url": "https://www.linkedin.com/in/yashkhivasara/",
            "email": "<EMAIL>"
        }
        
        print("   Testing /api/ingest/linkedin...")
        try:
            response = await self.client.post(
                f"{self.base_url}/api/ingest/linkedin",
                json=test_data,
                headers=headers
            )
            
            result = {
                "status_code": response.status_code,
                "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text,
                "success": response.status_code == 200
            }
            
            if response.status_code == 200:
                print("   ✅ LinkedIn endpoint working")
            else:
                print(f"   ❌ LinkedIn endpoint failed: {response.status_code}")
            
            return result
            
        except Exception as e:
            print(f"   ❌ Error testing LinkedIn endpoint: {e}")
            return {"error": str(e)}
    
    def analyze_original_curl_command(self):
        """Analyze the original curl command that failed."""
        print("\n🔍 Analyzing Original Curl Command...")
        
        original_command = """
        curl --location 'http://localhost:8000/api/ingest/resume' \\
        --header 'Content-Type: application/json' \\
        --header 'Authorization: Bearer [TOKEN_MISSING]' \\
        --data-raw '{
            "email": "<EMAIL>",
            "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf",
            "filename": "AbhishekBisht[5y_0m].pdf"
        }'
        """
        
        print("   Original command analysis:")
        print("   ❌ ISSUE 1: Missing Bearer token - '[TOKEN_MISSING]' is not a valid token")
        print("   ✅ GOOD: Correct endpoint URL")
        print("   ✅ GOOD: Correct Content-Type header")
        print("   ✅ GOOD: Valid JSON payload structure")
        print("   ⚠️  POTENTIAL ISSUE: Filename contains brackets which might cause URL encoding issues")
        
        print("\n   Expected response without valid token:")
        print("   - Status Code: 401 Unauthorized")
        print("   - Response Body: {'error': 'Unauthorized', 'message': 'Missing authentication token'}")
    
    def provide_corrected_commands(self, token: Optional[str] = None):
        """Provide corrected curl commands."""
        print("\n✅ Corrected Curl Commands...")
        
        if token:
            print("   With generated test token:")
            print(f"""
   curl --location 'http://localhost:8000/api/ingest/resume' \\
   --header 'Content-Type: application/json' \\
   --header 'Authorization: Bearer {token}' \\
   --data-raw '{{
       "email": "<EMAIL>",
       "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf",
       "filename": "AbhishekBisht[5y_0m].pdf"
   }}'
            """)
            
            print("   For vector-only processing:")
            print(f"""
   curl --location 'http://localhost:8000/api/ingest/resume-simple' \\
   --header 'Content-Type: application/json' \\
   --header 'Authorization: Bearer {token}' \\
   --data-raw '{{
       "email": "<EMAIL>",
       "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf",
       "filename": "AbhishekBisht[5y_0m].pdf"
   }}'
            """)
        else:
            print("   ❌ Cannot provide corrected commands - no valid token available")
            print("   💡 You need to set up JWT authentication first")
    
    def print_summary_and_recommendations(self, results: Dict[str, Any]):
        """Print summary and recommendations."""
        print("\n" + "=" * 80)
        print("📋 DIAGNOSTIC SUMMARY & RECOMMENDATIONS")
        print("=" * 80)
        
        # Basic connectivity
        if results.get("connectivity"):
            print("✅ API Connectivity: Working")
        else:
            print("❌ API Connectivity: Failed")
            print("   🔧 Check if the API server is running on localhost:8000")
            return
        
        # Authentication
        auth_results = results.get("authentication", {})
        working_auth = sum(1 for r in auth_results.values() if isinstance(r, dict) and r.get("requires_auth"))
        total_endpoints = len(auth_results)
        
        print(f"🔐 Authentication: {working_auth}/{total_endpoints} endpoints properly secured")
        
        # Token generation
        if results.get("token"):
            print("✅ JWT Token Generation: Working")
        else:
            print("❌ JWT Token Generation: Failed")
            print("   🔧 Set up JWT_PRIVATE_KEY in your .env file")
        
        # Endpoint testing
        endpoint_results = results.get("endpoints", {})
        working_endpoints = sum(1 for r in endpoint_results.values() if isinstance(r, dict) and r.get("success"))
        total_tested = len([r for r in endpoint_results.values() if not isinstance(r, dict) or "error" not in r or r.get("status_code")])
        
        print(f"📡 Endpoint Testing: {working_endpoints}/{total_tested} endpoints working")
        
        print("\n🎯 NEXT STEPS:")
        if not results.get("token"):
            print("1. Set up JWT authentication keys in your .env file")
            print("2. Generate a valid JWT token for testing")
        else:
            print("1. Use the generated JWT token for API calls")
            print("2. Test both resume processing endpoints")
            print("3. Monitor logs for detailed error information")
        
        print("4. Check environment variables are properly configured")
        print("5. Verify Qdrant and Supabase connections")


async def main():
    """Main diagnostic function."""
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    async with APIDiagnosticTool() as diagnostic:
        diagnostic.print_header()
        
        results = {}
        
        # Test basic connectivity
        results["connectivity"] = await diagnostic.test_basic_connectivity()
        
        if not results["connectivity"]:
            print("\n❌ Cannot proceed - API is not reachable")
            return
        
        # Test authentication requirements
        results["authentication"] = await diagnostic.test_authentication_requirements()
        
        # Try to generate test JWT
        test_token = diagnostic.generate_test_jwt()
        results["token"] = test_token is not None
        
        # Test endpoints with authentication
        if test_token:
            resume_results = await diagnostic.test_resume_endpoint_with_auth(test_token)
            linkedin_results = await diagnostic.test_linkedin_endpoint_with_auth(test_token)
            results["endpoints"] = {**resume_results, "linkedin": linkedin_results}
        
        # Analyze original command
        diagnostic.analyze_original_curl_command()
        
        # Provide corrected commands
        diagnostic.provide_corrected_commands(test_token)
        
        # Print summary
        diagnostic.print_summary_and_recommendations(results)


def generate_development_jwt_keys():
    """Generate development JWT keys if they don't exist."""
    print("\n🔑 JWT Key Setup for Development...")

    private_key_file = project_root / "private.key"
    public_key_file = project_root / "public.key"

    if private_key_file.exists() and public_key_file.exists():
        print("   ✅ JWT keys already exist")
        return True

    try:
        from cryptography.hazmat.primitives import serialization
        from cryptography.hazmat.primitives.asymmetric import rsa

        print("   🔧 Generating new RSA key pair for development...")

        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )

        # Serialize private key
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )

        # Serialize public key
        public_key = private_key.public_key()
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )

        # Write keys to files
        with open(private_key_file, 'wb') as f:
            f.write(private_pem)

        with open(public_key_file, 'wb') as f:
            f.write(public_pem)

        print("   ✅ JWT keys generated successfully")
        print(f"   📁 Private key: {private_key_file}")
        print(f"   📁 Public key: {public_key_file}")

        return True

    except ImportError:
        print("   ❌ cryptography package not available")
        print("   💡 Install with: pip install cryptography")
        return False
    except Exception as e:
        print(f"   ❌ Failed to generate keys: {e}")
        return False


def create_simple_test_token():
    """Create a simple test token for development."""
    print("\n🎫 Creating Simple Test Token...")

    try:
        # Simple payload for testing
        payload = {
            "sub": "test-user-123",
            "roles": ["service_extract"],
            "iat": int(time.time()),
            "exp": int(time.time()) + 3600  # 1 hour
        }

        # Use a simple secret for development
        secret = "development-secret-key-not-for-production"
        token = jwt.encode(payload, secret, algorithm="HS256")

        print("   ✅ Simple test token created")
        print("   ⚠️  This is for development only - uses HS256 with simple secret")
        print(f"   🎫 Token: {token}")

        return token

    except Exception as e:
        print(f"   ❌ Failed to create simple token: {e}")
        return None


if __name__ == "__main__":
    # Check if user wants to generate keys first
    if len(sys.argv) > 1 and sys.argv[1] == "--setup-keys":
        generate_development_jwt_keys()
        print("\n💡 Keys generated. Now run the diagnostic without --setup-keys")
        sys.exit(0)

    if len(sys.argv) > 1 and sys.argv[1] == "--simple-token":
        token = create_simple_test_token()
        if token:
            print(f"\n📋 Use this token in your curl commands:")
            print(f"Authorization: Bearer {token}")
        sys.exit(0)

    asyncio.run(main())
