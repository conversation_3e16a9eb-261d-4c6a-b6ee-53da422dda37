# Backend Architecture Plan
## Volunteer Matching System


---

## Executive Summary

This document outlines the technical architecture for a pure-backend volunteer matching system that processes LinkedIn profiles and résumés to create searchable volunteer profiles with vector-based similarity matching. The solution emphasizes production-ready scalability, security, and observability while leveraging managed cloud services for simplified operations.

### Key Objectives
- **Pure Backend Solution**: REST APIs with no UI components
- **Synchronous Processing**: Direct processing workflows within API endpoints
- **Vector Search Capabilities**: Semantic matching using sentence embeddings
- **Production-Ready**: Security, observability, and scalability built-in
- **Minimal Architecture**: Single API service with integrated processing
- **Managed Services**: Supabase PostgreSQL for simplified database management
- **Managed Services**: Supabase PostgreSQL for simplified database management

---

## System Architecture Overview

### High-Level Components

| Component | Purpose | Technology Stack |
|-----------|---------|------------------|
| **API Service** | REST endpoints with integrated processing | FastAPI, JWT Authentication |
| **Processing Engine** | LinkedIn & PDF processing within API | spaCy NLP, Browser-Use API |
| **Vector Engine** | Embeddings and vector storage | Sentence-Transformers, Qdrant |
| **Relational Database** | Structured data and metadata | Supabase PostgreSQL |
| **Vector Database** | Semantic search and similarity matching | Qdrant |

### Architecture Diagram
```
┌─────────────────────────────────────────────────────────────┐
│                    API Service (FastAPI)                    │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  │   Ingestion     │  │   Processing    │  │   Vector        │
│  │   Endpoints     │  │   Engine        │  │   Engine        │
│  │                 │  │                 │  │                 │
│  │ • LinkedIn URLs │  │ • Browser-Use   │  │ • Embeddings    │
│  │ • PDF URLs      │  │ • PDF Parse     │  │ • Similarity    │
│  │ • Validation    │  │ • NLP Extract   │  │ • Search        │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  │   Query         │  │   Authentication│  │   Monitoring    │
│  │   Endpoints     │  │   & Security    │  │   & Health      │
│  │                 │  │                 │  │                 │
│  │ • Profile Get   │  │ • JWT Validation│  │ • Metrics       │
│  │ • Vector Match  │  │ • RBAC          │  │ • Health Check  │
│  │ • Similarity    │  │ • Rate Limiting │  │ • Logging       │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘
└─────────────────────────────────────────────────────────────┘
                                 │
                ┌────────────────┼────────────────┐
                │                │                │
    ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
    │   Supabase      │ │   Qdrant        │ │   AWS Secrets   │
    │   PostgreSQL    │ │   (Vectors)     │ │   Manager       │
    │   (Metadata)    │ │                 │ │   (Optional)    │
    └─────────────────┘ └─────────────────┘ └─────────────────┘
```

---

## Detailed Component Specifications

### 1. API Service (FastAPI)

#### Core Responsibilities
- **Ingestion Endpoints**: Accept LinkedIn URLs and résumé PDF URLs with immediate processing
- **Processing Integration**: Direct processing of LinkedIn profiles and PDF documents
- **Vector Operations**: Generate embeddings and store vectors during request handling
- **Query Endpoints**: Retrieve volunteer profiles and perform similarity searches
- **Authentication**: JWT-based security with role-based access control
- **Observability**: Metrics collection and health monitoring

#### Key Endpoints
| Endpoint | Method | Purpose | Authentication | Processing |
|----------|--------|---------|----------------|------------|
| `/ingest/linkedin` | POST | Submit LinkedIn URL for processing | Required | Synchronous |
| `/ingest/resume` | POST | Submit PDF URL for processing | Required | Synchronous |
| `/volunteer/{id}` | GET | Retrieve complete volunteer profile | Required | Database Query |
| `/vector/{id}` | GET | Get vector representation | Required | Vector Database |
| `/match` | POST | Semantic similarity search | Required | Vector Search |
| `/healthz` | GET | Health check | Public | System Status |
| `/metrics` | GET | Prometheus metrics | Internal | Metrics Collection |

#### Integrated Processing Pipeline
1. **Request Validation**: Input validation and authentication
2. **Source Processing**: LinkedIn scraping or PDF parsing
3. **NLP Extraction**: Real-time text processing and feature extraction
4. **Vector Generation**: Embedding creation and storage
5. **Data Persistence**: Database updates and response generation

#### Security Features
- **JWT Authentication**: RS256 with role-based access (`admin`, `service_extract`, `viewer`)
- **HTTPS Only**: All communications encrypted
- **Secrets Management**: Environment variable configuration
- **Input Validation**: Comprehensive request validation
- **Rate Limiting**: API throttling and abuse prevention

### 2. Processing Engine (Integrated)

#### LinkedIn Processing
- **Browser-Use API Integration**: Direct API calls for profile scraping
- **Structured Data Extraction**: Real-time profile data processing
- **Rate Limiting**: Built-in throttling and retry mechanisms
- **Error Handling**: Graceful fallback and timeout management

#### PDF Processing
- **S3 Integration**: Direct download from presigned URLs
- **Multi-format Support**: PDF parsing with pdfplumber
- **OCR Fallback**: Tesseract for image-based documents
- **Memory Management**: Efficient processing without temporary storage

#### NLP Processing Pipeline
- **spaCy Integration**: Real-time NLP with `en_core_web_trf`
- **Custom Rule Matching**: Availability and work mode extraction
- **Feature Extraction**: Skills, experience, and qualification parsing
- **Data Normalization**: Consistent output formatting

#### Vector Generation
- **Sentence Transformers**: CPU-optimized embedding generation
- **Batch Processing**: Efficient vectorization within request context
- **Memory Optimization**: In-memory model caching
- **Quality Assurance**: Embedding validation and quality checks

#### Output Schema
```json
{
  "volunteer_id": "uuid",
  "profile_data": {
    "name": "string",
    "skills": ["array"],
    "experience": "string",
    "availability": "enum",
    "work_mode": "enum"
  },
  "vector": [384-dimensional array],
  "raw_text": "string",
  "metadata": {
    "source": "linkedin|resume",
    "processed_at": "timestamp",
    "confidence_score": "float"
  }
}
```

### 3. Database Integration

#### Supabase PostgreSQL Operations
- **Managed Database**: Fully managed PostgreSQL with automatic backups and scaling
- **SSL Connections**: Secure connections with built-in SSL/TLS encryption
- **Connection Pooling**: Efficient connection management through asyncpg
- **Upsert Operations**: Efficient insert/update operations with conflict resolution
- **Transaction Management**: ACID compliance for data consistency
- **Real-time Capabilities**: Built-in real-time subscriptions (optional for future use)

#### Qdrant Operations
- **Collection Management**: Automatic collection creation and optimization
- **Vector Upsert**: Direct vector storage during processing
- **Similarity Search**: Real-time semantic matching
- **Metadata Sync**: Coordinated updates between databases

---

## Data Architecture

### Supabase PostgreSQL Schema

#### Core Tables
```sql
-- Volunteer profiles table (Updated for multi-vector architecture)
CREATE TABLE public.volunteers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    headline TEXT,
    location VARCHAR(255),
    about_summary TEXT,
    current_company VARCHAR(255),
    current_position VARCHAR(255),
    skills TEXT[] DEFAULT '{}',
    vector_metadata JSONB DEFAULT '{}',
    languages_spoken TEXT[] DEFAULT '{}',
    source_types TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Processing audit table
CREATE TABLE public.processing_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    volunteer_id UUID REFERENCES public.volunteers(id) ON DELETE CASCADE,
    source_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'processing',
    source_url TEXT,
    error_message TEXT,
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for performance
CREATE INDEX idx_volunteers_email ON public.volunteers(email);
CREATE INDEX idx_volunteers_full_name ON public.volunteers(full_name);
CREATE INDEX idx_volunteers_current_company ON public.volunteers(current_company);
CREATE INDEX idx_volunteers_updated_at ON public.volunteers(updated_at);
CREATE INDEX idx_volunteers_source_types ON public.volunteers USING GIN(source_types);
CREATE INDEX idx_processing_logs_volunteer_id ON public.processing_logs(volunteer_id);
CREATE INDEX idx_processing_logs_status ON public.processing_logs(status);
CREATE INDEX idx_processing_logs_created_at ON public.processing_logs(created_at);
```

#### Profile Data Structure (JSONB)
```json
{
  "basic_info": {
    "full_name": "string",
    "headline": "string",
    "location": "string",
    "about_summary": "string"
  },
  "current_role": {
    "company": "string",
    "position": "string",
    "duration": "string"
  },
  "experience": [
    {
      "company": "string",
      "position": "string",
      "duration": "string",
      "description": "string"
    }
  ],
  "education": [
    {
      "institution": "string",
      "degree": "string",
      "field": "string",
      "duration": "string"
    }
  ],
  "skills": ["array of strings"],
  "certifications": [
    {
      "name": "string",
      "issuer": "string",
      "date": "string"
    }
  ],
  "volunteer_work": [
    {
      "organization": "string",
      "role": "string",
      "duration": "string",
      "description": "string"
    }
  ],
  "projects": [
    {
      "name": "string",
      "description": "string",
      "url": "string"
    }
  ]
}
```

### Qdrant Vector Schema

#### Multi-Vector Collection Configuration
```python
{
  "collection_name": "linkedin_profiles",
  "vectors_config": {
    "skills_vector": {
      "size": 1536,
      "distance": "Cosine"
    },
    "certifications_vector": {
      "size": 1536,
      "distance": "Cosine"
    },
    "experience_vector": {
      "size": 1536,
      "distance": "Cosine"
    },
    "summary_vector": {
      "size": 1536,
      "distance": "Cosine"
    },
    "languages_vector": {
      "size": 1536,
      "distance": "Cosine"
    },
    "volunteer_experience_vector": {
      "size": 1536,
      "distance": "Cosine"
    },
    "combined_vector": {
      "size": 1536,
      "distance": "Cosine"
    }
  },
  "payload_schema": {
    "volunteer_id": "keyword",
    "email": "keyword",
    "source_type": "keyword",
    "full_name": "text",
    "skills": "text[]",
    "current_company": "keyword",
    "location": "keyword",
    "created_at": "datetime"
  }
}
```

---

## Security Architecture

### Authentication & Authorization
- **JWT Tokens**: RS256 signed tokens with role-based claims
- **Role-Based Access Control (RBAC)**:
  - `admin`: Full system access
  - `service_extract`: Can ingest and process data
  - `viewer`: Read-only access to profiles and search
- **Rate Limiting**: Per-user and per-endpoint throttling
- **Input Validation**: Comprehensive request validation with Pydantic

### Data Security
- **Encryption in Transit**: HTTPS/TLS for all communications
- **Encryption at Rest**: Supabase provides automatic encryption
- **Connection Security**: SSL-required database connections
- **Secrets Management**: Environment variables with optional AWS Secrets Manager
- **PII Handling**: Email hashing for privacy protection

---

## Deployment Architecture

### Environment Configuration

#### Development
- **Database**: Supabase development project
- **Vector DB**: Local Qdrant instance
- **Logging**: Console output with DEBUG level
- **Authentication**: Relaxed validation for testing

#### Production
- **Database**: Supabase production project with connection pooling
- **Vector DB**: Qdrant Cloud or self-hosted with clustering
- **Logging**: Structured JSON logs with INFO level
- **Authentication**: Strict JWT validation with proper key rotation
- **Monitoring**: Prometheus metrics with alerting
- **SSL**: Enforced HTTPS with proper certificates

### Supabase Configuration

#### Connection Settings
```bash
# Primary connection string
DATABASE_URL=postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres

# Optional project details
SUPABASE_URL=https://[PROJECT-REF].supabase.co
SUPABASE_ANON_KEY=[ANON-KEY]

# Connection pool settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
POSTGRES_SSL_MODE=require
```

#### Benefits of Supabase
- **Managed Infrastructure**: No database administration overhead
- **Automatic Backups**: Point-in-time recovery and automated backups
- **Scaling**: Automatic scaling based on usage
- **Security**: Built-in security features and SSL encryption
- **Monitoring**: Built-in database monitoring and analytics
- **Real-time**: Optional real-time subscriptions for future features
- **Extensions**: Support for PostgreSQL extensions like vector operations

---

## Monitoring & Observability

### Health Checks
- **Database Health**: Supabase connection and table accessibility
- **Vector Database**: Qdrant connectivity and collection status
- **Browser-Use Client**: LinkedIn automation service health
- **Processing Pipeline**: End-to-end processing validation

### Metrics Collection
- **Request Metrics**: Response times, error rates, throughput
- **Processing Metrics**: LinkedIn extraction success rates, processing times
- **Database Metrics**: Connection pool usage, query performance
- **Vector Metrics**: Embedding generation times, search performance

### Logging Strategy
- **Structured Logging**: JSON format with request IDs
- **Log Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Request Tracing**: End-to-end request tracking
- **Error Handling**: Comprehensive error logging with context

---

## Performance Considerations

### Database Optimization
- **Connection Pooling**: Efficient connection management with asyncpg
- **Indexing Strategy**: Optimized indexes for common query patterns
- **Query Optimization**: Efficient SQL queries with proper joins
- **JSONB Performance**: Optimized JSONB queries for profile data

### Vector Operations
- **Batch Processing**: Efficient embedding generation in batches
- **Memory Management**: Optimized model loading and caching
- **Search Optimization**: Qdrant-optimized similarity searches
- **Indexing**: Proper vector indexing for fast retrieval

### API Performance
- **Async Processing**: Non-blocking I/O operations
- **Response Caching**: Intelligent caching strategies
- **Request Validation**: Fast input validation with Pydantic
- **Background Tasks**: Efficient background processing with FastAPI

---

## Scalability Strategy

### Horizontal Scaling
- **API Service**: Multiple FastAPI instances behind load balancer
- **Database**: Supabase automatic scaling and read replicas
- **Vector Database**: Qdrant clustering for high availability
- **Processing**: Distributed processing across multiple instances

### Vertical Scaling
- **CPU Optimization**: Efficient NLP model usage
- **Memory Management**: Optimized model caching and data structures
- **I/O Optimization**: Efficient database and network operations
- **Resource Monitoring**: Continuous performance monitoring and optimization

---

## Future Enhancements

### Planned Features
- **Real-time Updates**: Supabase real-time subscriptions for live updates
- **Advanced Search**: Full-text search integration with PostgreSQL
- **ML Pipeline**: Enhanced skill extraction with custom models
- **Analytics**: Advanced analytics and reporting capabilities
- **API Versioning**: Versioned API endpoints for backward compatibility

### Technical Improvements
- **Caching Layer**: Redis integration for improved performance
- **Message Queue**: Optional queue system for high-volume processing
- **Microservices**: Service decomposition for independent scaling
- **GraphQL**: GraphQL endpoint for flexible data querying

---