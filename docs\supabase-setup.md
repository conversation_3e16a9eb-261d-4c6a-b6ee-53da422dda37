# Supabase Setup Guide

This guide walks you through setting up Supabase PostgreSQL for the Skill Extractor project.

## 🚀 Quick Setup

### 1. Create Supabase Account
1. Go to [supabase.com](https://supabase.com)
2. Sign up with GitHub, Google, or email
3. Verify your email if required

### 2. Create New Project
1. Click "New Project" in your dashboard
2. Choose your organization (or create one)
3. Fill in project details:
   - **Name**: `skill-extractor` (or your preferred name)
   - **Database Password**: Generate a strong password (save this!)
   - **Region**: Choose closest to your location
   - **Pricing Plan**: Free tier is sufficient for development

4. Click "Create new project"
5. Wait 2-3 minutes for project initialization

### 3. Get Connection Details

#### Option A: Connection String (Recommended)
1. Go to **Settings** → **Database**
2. Scroll down to "Connection string"
3. Select **URI** tab
4. Copy the connection string (it looks like):
   ```
   postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres
   ```

#### Option B: Individual Connection Details
If you prefer individual settings:
- **Host**: `db.[PROJECT-REF].supabase.co`
- **Port**: `5432`
- **Database**: `postgres`
- **Username**: `postgres`
- **Password**: [Your project password]

### 4. Configure Environment
Update your `.env` file:

```bash
# Replace [YOUR-PASSWORD] and [PROJECT-REF] with actual values
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres

# Optional: For reference or future features
SUPABASE_URL=https://[PROJECT-REF].supabase.co
SUPABASE_ANON_KEY=[YOUR-ANON-KEY]
```

### 5. Test Connection
Run this Python script to test your connection:

```python
import asyncio
import asyncpg

async def test_connection():
    try:
        # Replace with your actual DATABASE_URL
        conn = await asyncpg.connect("postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres")
        
        # Test basic query
        result = await conn.fetchval("SELECT version()")
        print(f"✅ Connected successfully!")
        print(f"PostgreSQL version: {result}")
        
        # Test table creation (optional)
        await conn.execute("CREATE TABLE IF NOT EXISTS test_table (id SERIAL PRIMARY KEY, name TEXT)")
        await conn.execute("DROP TABLE test_table")
        print("✅ Table operations working!")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_connection())
```

## 🔧 Advanced Configuration

### Database Settings

#### Connection Pooling
Supabase handles connection pooling automatically, but you can configure your application pool:

```bash
# In your .env file
DB_POOL_SIZE=10          # Number of connections to maintain
DB_MAX_OVERFLOW=20       # Maximum additional connections
POSTGRES_SSL_MODE=require # Always use SSL with Supabase
```

#### Performance Settings
For production workloads, consider upgrading your Supabase plan for:
- More concurrent connections
- Better performance
- Larger database size
- Point-in-time recovery

### Security Configuration

#### Row Level Security (RLS)
Supabase supports Row Level Security. For this project, we'll manage security at the application level with JWT tokens, but you can enable RLS for additional protection:

```sql
-- Enable RLS on volunteers table (optional)
ALTER TABLE public.volunteers ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users
CREATE POLICY "Allow authenticated access" ON public.volunteers
FOR ALL USING (auth.role() = 'authenticated');
```

#### API Keys
Your Supabase project comes with two API keys:
- **Anon Key**: For client-side operations (public)
- **Service Role Key**: For server-side operations (secret)

For this backend-only project, you typically don't need these keys since we're connecting directly to PostgreSQL.

## 🗄️ Database Schema

The application will automatically create these tables when it starts:

### Tables Created
1. **`public.volunteers`** - Main volunteer profile data
2. **`public.processing_logs`** - Audit trail for processing operations

### Manual Schema Setup (Optional)
If you want to create the schema manually:

```sql
-- Volunteer profiles table (Updated for multi-vector architecture)
CREATE TABLE public.volunteers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    headline TEXT,
    location VARCHAR(255),
    about_summary TEXT,
    current_company VARCHAR(255),
    current_position VARCHAR(255),
    skills TEXT[] DEFAULT '{}',
    vector_metadata JSONB DEFAULT '{}',
    languages_spoken TEXT[] DEFAULT '{}',
    source_types TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Processing audit table
CREATE TABLE public.processing_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    volunteer_id UUID REFERENCES public.volunteers(id) ON DELETE CASCADE,
    source_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'processing',
    source_url TEXT,
    error_message TEXT,
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for performance
CREATE INDEX idx_volunteers_email ON public.volunteers(email);
CREATE INDEX idx_volunteers_full_name ON public.volunteers(full_name);
CREATE INDEX idx_volunteers_current_company ON public.volunteers(current_company);
CREATE INDEX idx_volunteers_updated_at ON public.volunteers(updated_at);
CREATE INDEX idx_volunteers_source_types ON public.volunteers USING GIN(source_types);
CREATE INDEX idx_processing_logs_volunteer_id ON public.processing_logs(volunteer_id);
CREATE INDEX idx_processing_logs_status ON public.processing_logs(status);
CREATE INDEX idx_processing_logs_created_at ON public.processing_logs(created_at);
```

## 📊 Monitoring & Management

### Supabase Dashboard Features

#### Database Tab
- **Tables**: View and edit table data
- **Functions**: Manage PostgreSQL functions
- **Extensions**: Enable PostgreSQL extensions
- **Roles**: Manage database roles and permissions

#### Logs Tab
- **Postgres Logs**: Database query logs and errors
- **Realtime Logs**: WebSocket connection logs
- **Function Logs**: Edge function execution logs

#### Settings Tab
- **General**: Project settings and metadata
- **Database**: Connection details and configuration
- **API**: API keys and configuration
- **Auth**: Authentication settings (if using Supabase Auth)

### Performance Monitoring
Monitor your database performance:
1. Go to **Reports** in your Supabase dashboard
2. View metrics like:
   - Database size
   - Active connections
   - Query performance
   - API requests

## 🚨 Troubleshooting

### Common Issues

#### Connection Refused
```
Error: connection refused
```
**Solutions:**
- Check your connection string format
- Verify project is fully initialized (wait 2-3 minutes after creation)
- Ensure you're using the correct project reference

#### SSL Certificate Issues
```
Error: SSL connection failed
```
**Solutions:**
- Ensure `ssl=require` or `sslmode=require` in connection string
- Update your PostgreSQL client libraries
- Check firewall settings

#### Authentication Failed
```
Error: password authentication failed
```
**Solutions:**
- Double-check your database password
- Reset password in Supabase dashboard if needed
- Ensure no special characters are URL-encoded incorrectly

#### Too Many Connections
```
Error: too many connections
```
**Solutions:**
- Reduce your connection pool size
- Upgrade your Supabase plan for more connections
- Check for connection leaks in your application

### Getting Help

1. **Supabase Documentation**: [docs.supabase.com](https://docs.supabase.com)
2. **Supabase Discord**: [discord.supabase.com](https://discord.supabase.com)
3. **Supabase GitHub**: [github.com/supabase/supabase](https://github.com/supabase/supabase)

## 🎯 Next Steps

After setting up Supabase:

1. ✅ Update your `.env` file with the connection string
2. ✅ Test the connection using the Python script above
3. ✅ Start your skill-extractor API service
4. ✅ Verify tables are created automatically
5. ✅ Test the `/healthz` endpoint to confirm database connectivity

Your Supabase PostgreSQL database is now ready for the Skill Extractor project! 