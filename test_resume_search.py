#!/usr/bin/env python3
"""
Test script for validating resume semantic search functionality.

This script tests the current resume storage implementation to verify
that semantic search works effectively for finding candidates by skills,
experience, and other criteria.
"""

import requests
import json
import time
from typing import List, Dict, Any, Optional


class ResumeSearchTester:
    """Test class for validating resume search functionality."""
    
    def __init__(self, base_url: str = "http://localhost:8000", token: str = None):
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}" if token else ""
        }
    
    def test_vector_search(self, query_text: str, top_k: int = 5) -> Dict[str, Any]:
        """
        Test vector-based search using query text.

        Args:
            query_text: Natural language query
            top_k: Number of results to return

        Returns:
            Search results and metadata
        """
        url = f"{self.base_url}/api/query/resumes/search"
        params = {
            "query_text": query_text,
            "vector_field": "combined_vector",
            "top_k": top_k,
            "score_threshold": 0.5
        }
        
        try:
            start_time = time.time()
            response = requests.post(url, headers=self.headers, params=params)
            query_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                results = response.json()
                return {
                    "success": True,
                    "query": query_text,
                    "results": results,
                    "query_time_ms": query_time,
                    "status_code": response.status_code
                }
            else:
                return {
                    "success": False,
                    "query": query_text,
                    "error": response.text,
                    "status_code": response.status_code,
                    "query_time_ms": query_time
                }
                
        except Exception as e:
            return {
                "success": False,
                "query": query_text,
                "error": str(e),
                "status_code": None,
                "query_time_ms": None
            }
    
    def test_multiple_queries(self, queries: List[str]) -> List[Dict[str, Any]]:
        """
        Test multiple search queries to validate semantic understanding.
        
        Args:
            queries: List of test queries
            
        Returns:
            List of test results
        """
        results = []
        
        for query in queries:
            print(f"\n🔍 Testing query: '{query}'")
            result = self.test_vector_search(query)
            results.append(result)
            
            if result["success"]:
                search_results = result["results"]
                print(f"✅ Found {search_results.get('total_results', 0)} results in {result['query_time_ms']:.2f}ms")
                
                # Display top results
                for i, res in enumerate(search_results.get("results", [])[:3]):
                    score = res.get("score", 0)
                    metadata = res.get("metadata", {})
                    email = metadata.get("email", "unknown")
                    filename = metadata.get("filename", "unknown")
                    print(f"  {i+1}. Score: {score:.3f} | {email} | {filename}")
            else:
                print(f"❌ Query failed: {result['error']}")
        
        return results
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """
        Run a comprehensive test suite for resume search functionality.
        
        Returns:
            Complete test results and analysis
        """
        print("🚀 Starting Resume Search Validation Tests")
        print("=" * 60)
        
        # Test queries covering different search scenarios
        test_queries = [
            # Skills-based searches
            "marketing experience",
            "software developer Python",
            "project management agile",
            "data analysis SQL",
            "graphic design Adobe",
            
            # Experience level searches
            "senior developer 5 years",
            "entry level marketing",
            "experienced manager",
            
            # Industry-specific searches
            "healthcare professional",
            "financial analyst",
            "education teacher",
            
            # Combination searches
            "marketing manager with digital experience",
            "Python developer with machine learning",
            "project manager with technical background"
        ]
        
        # Run all tests
        test_results = self.test_multiple_queries(test_queries)
        
        # Analyze results
        successful_tests = [r for r in test_results if r["success"]]
        failed_tests = [r for r in test_results if not r["success"]]
        
        # Calculate statistics
        total_tests = len(test_results)
        success_rate = len(successful_tests) / total_tests * 100 if total_tests > 0 else 0
        avg_query_time = sum(r["query_time_ms"] for r in successful_tests) / len(successful_tests) if successful_tests else 0
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Successful: {len(successful_tests)} ({success_rate:.1f}%)")
        print(f"Failed: {len(failed_tests)}")
        print(f"Average Query Time: {avg_query_time:.2f}ms")
        
        if failed_tests:
            print("\n❌ Failed Tests:")
            for test in failed_tests:
                print(f"  - '{test['query']}': {test['error']}")
        
        return {
            "total_tests": total_tests,
            "successful_tests": len(successful_tests),
            "failed_tests": len(failed_tests),
            "success_rate": success_rate,
            "average_query_time_ms": avg_query_time,
            "detailed_results": test_results
        }


def main():
    """Main function to run the resume search tests."""
    
    # Configuration
    API_BASE_URL = "http://localhost:8000"
    JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzZXJ2aWNlX2V4dHJhY3QiLCJyb2xlcyI6WyJzZXJ2aWNlX2V4dHJhY3QiXSwiZXhwIjoxNzM2MjY3NzI5fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"
    
    # Initialize tester
    tester = ResumeSearchTester(base_url=API_BASE_URL, token=JWT_TOKEN)
    
    # Run comprehensive tests
    results = tester.run_comprehensive_test()
    
    # Save results to file
    with open("resume_search_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: resume_search_test_results.json")
    
    # Recommendations based on results
    print("\n🎯 RECOMMENDATIONS:")
    if results["success_rate"] >= 80:
        print("✅ Search functionality is working well!")
        print("   Consider adding more specialized vector fields for enhanced precision.")
    elif results["success_rate"] >= 50:
        print("⚠️  Search functionality is partially working.")
        print("   Review failed queries and consider improving metadata storage.")
    else:
        print("❌ Search functionality needs significant improvement.")
        print("   Check vector generation and search endpoint implementation.")


if __name__ == "__main__":
    main()
