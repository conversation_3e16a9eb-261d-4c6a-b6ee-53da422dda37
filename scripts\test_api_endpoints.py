#!/usr/bin/env python3
"""
Comprehensive API endpoint testing script.

This script tests all WeDoGood API endpoints with proper authentication
and provides detailed feedback on what's working and what needs fixing.
"""

import asyncio
import json
import os
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional

import httpx

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from common.logging import get_logger

logger = get_logger(__name__)


class APIEndpointTester:
    """Comprehensive API endpoint tester."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_token = None
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def print_header(self):
        """Print test header."""
        print("=" * 80)
        print("🧪 WEDOGOOD API ENDPOINT COMPREHENSIVE TESTING")
        print("=" * 80)
        print(f"Base URL: {self.base_url}")
        print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def generate_test_token(self) -> Optional[str]:
        """Generate a test JWT token."""
        print("🎫 Generating Test JWT Token...")
        
        try:
            result = subprocess.run([
                sys.executable, "scripts/generate_test_token.py"
            ], capture_output=True, text=True, cwd=project_root)
            
            if result.returncode == 0:
                # Read the token from the saved file
                token_file = project_root / "dev_token.txt"
                if token_file.exists():
                    with open(token_file, 'r') as f:
                        token = f.read().strip()
                    print("   ✅ Test token generated successfully")
                    return token
                else:
                    print("   ❌ Token file not found")
                    return None
            else:
                print(f"   ❌ Token generation failed: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"   ❌ Error generating token: {e}")
            return None
    
    async def test_server_connectivity(self) -> bool:
        """Test basic server connectivity."""
        print("📡 Testing Server Connectivity...")
        
        try:
            response = await self.client.get(f"{self.base_url}/")
            
            if response.status_code == 200:
                print("   ✅ Server is reachable and responding")
                data = response.json()
                print(f"   📋 Service: {data.get('service', 'unknown')}")
                print(f"   📋 Version: {data.get('version', 'unknown')}")
                print(f"   📋 Environment: {data.get('environment', 'unknown')}")
                return True
            else:
                print(f"   ❌ Server returned status code: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Failed to connect to server: {e}")
            return False
    
    async def test_health_endpoint(self) -> bool:
        """Test health endpoint."""
        print("\n🏥 Testing Health Endpoint...")
        
        try:
            response = await self.client.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                print("   ✅ Health endpoint working")
                data = response.json()
                print(f"   📋 Status: {data.get('status', 'unknown')}")
                return True
            else:
                print(f"   ❌ Health endpoint failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Health endpoint error: {e}")
            return False
    
    async def test_authentication_behavior(self) -> Dict[str, Any]:
        """Test authentication behavior on protected endpoints."""
        print("\n🔐 Testing Authentication Behavior...")
        
        endpoints_to_test = [
            "/api/ingest/resume",
            "/api/ingest/resume-simple",
            "/api/ingest/linkedin"
        ]
        
        results = {}
        
        for endpoint in endpoints_to_test:
            print(f"   Testing {endpoint}...")
            
            try:
                # Test without authentication
                response = await self.client.post(
                    f"{self.base_url}{endpoint}",
                    json={"test": "data"}
                )
                
                results[endpoint] = {
                    "status_code": response.status_code,
                    "requires_auth": response.status_code in [401, 403],
                    "response_preview": response.text[:100] if response.text else None
                }
                
                if response.status_code == 401:
                    print(f"     ✅ Correctly requires authentication (401)")
                elif response.status_code == 403:
                    print(f"     ✅ Correctly requires authorization (403)")
                elif response.status_code == 404:
                    print(f"     ❌ Endpoint not found (404) - routing issue")
                else:
                    print(f"     ⚠️  Unexpected status: {response.status_code}")
                    
            except Exception as e:
                print(f"     ❌ Error testing {endpoint}: {e}")
                results[endpoint] = {"error": str(e)}
        
        return results
    
    async def test_resume_endpoints_with_auth(self) -> Dict[str, Any]:
        """Test resume endpoints with authentication."""
        print("\n📄 Testing Resume Endpoints with Authentication...")
        
        if not self.test_token:
            print("   ⚠️  No authentication token available")
            return {"error": "no_token"}
        
        headers = {
            "Authorization": f"Bearer {self.test_token}",
            "Content-Type": "application/json"
        }
        
        test_data = {
            "email": "<EMAIL>",
            "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf",
            "filename": "AbhishekBisht[5y_0m].pdf"
        }
        
        results = {}
        
        # Test original resume endpoint
        print("   Testing /api/ingest/resume...")
        try:
            response = await self.client.post(
                f"{self.base_url}/api/ingest/resume",
                json=test_data,
                headers=headers
            )
            
            results["resume"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response_preview": response.text[:200] if response.text else None
            }
            
            if response.status_code == 200:
                print("     ✅ Original resume endpoint working")
            elif response.status_code == 404:
                print("     ❌ Endpoint not found (404) - check routing")
            else:
                print(f"     ❌ Failed with status: {response.status_code}")
                
        except Exception as e:
            print(f"     ❌ Error: {e}")
            results["resume"] = {"error": str(e)}
        
        # Test vector-only resume endpoint
        print("   Testing /api/ingest/resume-simple...")
        try:
            response = await self.client.post(
                f"{self.base_url}/api/ingest/resume-simple",
                json=test_data,
                headers=headers
            )
            
            results["resume_simple"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response_preview": response.text[:200] if response.text else None
            }
            
            if response.status_code == 200:
                print("     ✅ Vector-only resume endpoint working")
            elif response.status_code == 404:
                print("     ❌ Endpoint not found (404) - check routing")
            else:
                print(f"     ❌ Failed with status: {response.status_code}")
                
        except Exception as e:
            print(f"     ❌ Error: {e}")
            results["resume_simple"] = {"error": str(e)}
        
        return results
    
    def provide_corrected_curl_commands(self):
        """Provide corrected curl commands."""
        print("\n✅ CORRECTED CURL COMMANDS")
        print("=" * 60)
        
        if self.test_token:
            print("🎯 For your original resume processing request:")
            print(f"""
curl --location '{self.base_url}/api/ingest/resume' \\
--header 'Content-Type: application/json' \\
--header 'Authorization: Bearer {self.test_token}' \\
--data-raw '{{
    "email": "<EMAIL>",
    "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf",
    "filename": "AbhishekBisht[5y_0m].pdf"
}}'
            """)
            
            print("🎯 For vector-only resume processing (recommended):")
            print(f"""
curl --location '{self.base_url}/api/ingest/resume-simple' \\
--header 'Content-Type: application/json' \\
--header 'Authorization: Bearer {self.test_token}' \\
--data-raw '{{
    "email": "<EMAIL>",
    "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf",
    "filename": "AbhishekBisht[5y_0m].pdf"
}}'
            """)
        else:
            print("❌ Cannot provide corrected commands - no valid token available")
            print("💡 Generate a token first: python scripts/generate_test_token.py")
    
    def print_summary_and_recommendations(self, results: Dict[str, Any]):
        """Print summary and recommendations."""
        print("\n" + "=" * 80)
        print("📊 TEST SUMMARY & RECOMMENDATIONS")
        print("=" * 80)
        
        # Server connectivity
        if results.get("connectivity"):
            print("✅ Server Connectivity: Working")
        else:
            print("❌ Server Connectivity: Failed")
            print("   🔧 Start the server: python run.py --reload")
            return
        
        # Health endpoint
        if results.get("health"):
            print("✅ Health Endpoint: Working")
        else:
            print("❌ Health Endpoint: Failed")
        
        # Authentication
        auth_results = results.get("authentication", {})
        working_auth = sum(1 for r in auth_results.values() 
                          if isinstance(r, dict) and r.get("requires_auth"))
        total_endpoints = len([r for r in auth_results.values() 
                              if isinstance(r, dict) and "status_code" in r])
        
        print(f"🔐 Authentication: {working_auth}/{total_endpoints} endpoints properly secured")
        
        # Endpoint testing
        endpoint_results = results.get("endpoints", {})
        working_endpoints = sum(1 for r in endpoint_results.values() 
                               if isinstance(r, dict) and r.get("success"))
        total_tested = len([r for r in endpoint_results.values() 
                           if isinstance(r, dict) and "status_code" in r])
        
        print(f"📡 Endpoint Testing: {working_endpoints}/{total_tested} endpoints working")
        
        # Specific issues
        print("\n🔍 SPECIFIC ISSUES FOUND:")
        
        for endpoint, result in auth_results.items():
            if isinstance(result, dict) and result.get("status_code") == 404:
                print(f"❌ {endpoint}: Returns 404 (Not Found) - routing configuration issue")
        
        print("\n🎯 RECOMMENDATIONS:")
        
        if any(r.get("status_code") == 404 for r in auth_results.values() if isinstance(r, dict)):
            print("1. Check API router configuration in services/api/main.py")
            print("2. Verify endpoint prefixes are correct (/api/ingest/)")
            print("3. Ensure all routers are properly included")
        
        if results.get("token"):
            print("4. Use the generated JWT token for authenticated requests")
            print("5. Test with the corrected curl commands provided above")
        else:
            print("4. Generate a JWT token: python scripts/generate_test_token.py")
        
        print("6. Use vector-only endpoint (/api/ingest/resume-simple) for better reliability")
    
    async def run_comprehensive_test(self):
        """Run comprehensive API testing."""
        self.print_header()
        
        results = {}
        
        # Generate test token
        self.test_token = self.generate_test_token()
        results["token"] = self.test_token is not None
        
        # Test server connectivity
        results["connectivity"] = await self.test_server_connectivity()
        
        if not results["connectivity"]:
            print("\n❌ Cannot proceed - server is not reachable")
            print("💡 Start the server: python run.py --reload")
            return results
        
        # Test health endpoint
        results["health"] = await self.test_health_endpoint()
        
        # Test authentication behavior
        results["authentication"] = await self.test_authentication_behavior()
        
        # Test endpoints with authentication
        if self.test_token:
            results["endpoints"] = await self.test_resume_endpoints_with_auth()
        
        # Provide corrected commands
        self.provide_corrected_curl_commands()
        
        # Print summary
        self.print_summary_and_recommendations(results)
        
        return results


async def main():
    """Main function."""
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Test with default port first, then try alternative
    ports_to_try = [8000, 8001, 8080]
    
    for port in ports_to_try:
        base_url = f"http://localhost:{port}"
        print(f"🔍 Trying server on port {port}...")
        
        async with APIEndpointTester(base_url) as tester:
            # Quick connectivity test
            try:
                response = await tester.client.get(f"{base_url}/", timeout=5.0)
                if response.status_code == 200:
                    print(f"✅ Found server on port {port}")
                    await tester.run_comprehensive_test()
                    return
            except:
                continue
    
    print("❌ No server found on any port. Please start the server:")
    print("   python run.py --reload")
    print("   or")
    print("   python -m uvicorn services.api.main:app --host 0.0.0.0 --port 8000 --reload")


if __name__ == "__main__":
    asyncio.run(main())
