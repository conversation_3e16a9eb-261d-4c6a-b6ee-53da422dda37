# Vector-Only Resume Processing

## Overview

The WeDoGood project now includes a simplified vector-only approach for resume processing that stores complete resume content as single vector embeddings without structured parsing. This approach is more robust for resumes with varying formats and ensures all content is captured for semantic search.

## Key Features

- **Simplified Processing**: Converts entire PDF resume content into a single vector embedding
- **Format Agnostic**: Works with any resume format without requiring specific section parsing
- **Robust Storage**: Stores vectors in Qdrant with minimal metadata for efficient search
- **Email Linking**: Links resume records to volunteer profiles via email for consistency
- **Error Resilient**: Handles varying resume formats that might fail with structured parsing

## Architecture

### Storage Strategy
- **Vector Storage**: Complete resume content stored as vector embeddings in Qdrant
- **Collection**: Uses the existing "resumes" collection with `combined_vector` field
- **Metadata**: Minimal metadata including email, S3 URL, text length, and timestamps
- **Point ID**: Deterministic UUID generated from email for consistent linking

### Processing Pipeline
1. **PDF Download**: Download PDF from S3 URL
2. **Text Extraction**: Extract complete text content using multiple strategies
3. **Vector Generation**: Generate single embedding from entire resume text
4. **Qdrant Storage**: Store vector and minimal metadata in Qdrant collection

## API Endpoints

### POST /api/ingest/resume-simple

Processes PDF resumes using the simplified vector-only approach.

#### Request
```json
{
  "email": "<EMAIL>",
  "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/resume.pdf",
  "filename": "john_doe_resume.pdf"
}
```

#### Response (Success)
```json
{
  "volunteer_id": "123e4567-e89b-12d3-a456-************",
  "status": "completed",
  "message": "Resume vector processing completed <NAME_EMAIL>. Processed 2500 characters in 1.50 seconds.",
  "estimated_completion": null
}
```

#### Response (Error)
```json
{
  "detail": {
    "error": "extraction_error",
    "message": "Insufficient text extracted from PDF",
    "email": "<EMAIL>",
    "processing_approach": "vector_only"
  }
}
```

## Usage Examples

### Python Client
```python
import asyncio
from services.api.clients.resume_service import process_resume_vector_only

async def process_resume():
    result = await process_resume_vector_only(
        email="<EMAIL>",
        resume_s3_url="https://skill-assessment-test.s3.amazonaws.com/pdfs/resume.pdf",
        filename="resume.pdf"
    )
    
    if result.success:
        print(f"Success! Point ID: {result.point_id}")
        print(f"Text length: {result.text_length} characters")
        print(f"Processing time: {result.processing_time:.2f} seconds")
    else:
        print(f"Failed: {result.error_message}")

asyncio.run(process_resume())
```

### cURL Request
```bash
curl -X POST "http://localhost:8000/api/ingest/resume-simple" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/pdfs/resume.pdf",
    "filename": "resume.pdf"
  }'
```

## Testing

### Quick Test Script
Run a quick test with the default test file:
```bash
cd scripts
python quick_test_vector_resume.py
```

### Comprehensive Test Suite
Run the full test suite:
```bash
cd scripts
python test_vector_only_resume.py
```

### Unit Tests
Run the unit tests:
```bash
pytest tests/test_resume_vector_only.py -v
```

## Configuration

### Environment Variables
Ensure these environment variables are set in your `.env` file:
```
QDRANT_URL=https://your-qdrant-instance.com
QDRANT_API_KEY=your-api-key
SENTENCE_TRANSFORMER_MODEL=all-MiniLM-L6-v2
```

### Test Bucket
The testing functionality uses the `s3://skill-assessment-test/pdfs/` bucket for validation.

## Comparison with Complex Processing

| Aspect | Vector-Only | Complex Processing |
|--------|-------------|-------------------|
| **Parsing** | None (full text) | Structured sections |
| **Vectors** | Single combined vector | 7 separate vectors |
| **Metadata** | Minimal | Complete profile data |
| **Robustness** | High (format agnostic) | Medium (format dependent) |
| **Processing Time** | Faster | Slower |
| **Search Capability** | Full content search | Section-specific search |

## Error Handling

The vector-only approach handles various error scenarios:

- **extraction_error**: Insufficient text extracted from PDF
- **vector_error**: Failed to generate vector embedding
- **storage_error**: Failed to store in Qdrant
- **initialization_error**: Service initialization failed
- **unexpected_error**: Unexpected processing error

## Best Practices

1. **Use for Varied Formats**: Prefer vector-only for resumes with non-standard formats
2. **Batch Processing**: Process multiple resumes with delays to avoid rate limits
3. **Error Monitoring**: Monitor error rates and types for system health
4. **Vector Search**: Use semantic search capabilities for finding relevant candidates
5. **Email Consistency**: Ensure email addresses are consistent across systems

## Future Enhancements

- **Hybrid Approach**: Combine vector-only with optional structured parsing
- **Multi-Language Support**: Enhanced support for non-English resumes
- **Vector Optimization**: Fine-tuned embeddings for resume-specific content
- **Search Interface**: Dedicated search endpoints for vector-based candidate matching

## Support

For issues or questions about vector-only resume processing:
1. Check the logs for detailed error information
2. Run the test scripts to verify system functionality
3. Review the API documentation for proper request formatting
4. Ensure environment variables are correctly configured
