-- Resume metadata table for storing resume processing information
-- This table stores metadata about processed resumes and links to volunteer records via email

CREATE TABLE IF NOT EXISTS public.resume_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    volunteer_id UUID,
    resume_s3_url TEXT NOT NULL,
    filename VA<PERSON>HAR(255),
    processing_status VARCHAR(50) NOT NULL DEFAULT 'processing',
    extraction_status VARCHAR(50) NOT NULL DEFAULT 'pending',
    qdrant_point_id UUID,
    skills_extracted TEXT[] DEFAULT '{}',
    languages_extracted TEXT[] DEFAULT '{}',
    full_name_extracted VARCHAR(255),
    location_extracted VARCHAR(255),
    phone_extracted VARCHAR(255),
    summary_extracted TEXT,
    work_experience_count INTEGER DEFAULT 0,
    education_count INTEGER DEFAULT 0,
    certifications_count INTEGER DEFAULT 0,
    volunteer_experience_count INTEGER DEFAULT 0,
    projects_count INTEGER DEFAULT 0,
    processing_time_ms INTEGER,
    error_message TEXT,
    error_type VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraint to volunteers table (optional, allows orphaned resumes)
    CONSTRAINT fk_resume_volunteer 
        FOREIGN KEY (volunteer_id) 
        REFERENCES public.volunteers(id) 
        ON DELETE SET NULL,
    
    -- Unique constraint to prevent duplicate resume processing
    CONSTRAINT unique_email_resume_url 
        UNIQUE (email, resume_s3_url)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_resume_metadata_email ON public.resume_metadata(email);
CREATE INDEX IF NOT EXISTS idx_resume_metadata_volunteer_id ON public.resume_metadata(volunteer_id);
CREATE INDEX IF NOT EXISTS idx_resume_metadata_processing_status ON public.resume_metadata(processing_status);
CREATE INDEX IF NOT EXISTS idx_resume_metadata_extraction_status ON public.resume_metadata(extraction_status);
CREATE INDEX IF NOT EXISTS idx_resume_metadata_created_at ON public.resume_metadata(created_at);
CREATE INDEX IF NOT EXISTS idx_resume_metadata_qdrant_point_id ON public.resume_metadata(qdrant_point_id);
CREATE INDEX IF NOT EXISTS idx_resume_metadata_skills ON public.resume_metadata USING GIN(skills_extracted);

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_resume_metadata_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at on row updates
DROP TRIGGER IF EXISTS trigger_update_resume_metadata_updated_at ON public.resume_metadata;
CREATE TRIGGER trigger_update_resume_metadata_updated_at
    BEFORE UPDATE ON public.resume_metadata
    FOR EACH ROW
    EXECUTE FUNCTION update_resume_metadata_updated_at();

-- Comments for documentation
COMMENT ON TABLE public.resume_metadata IS 'Stores metadata about processed resumes and links to volunteer records';
COMMENT ON COLUMN public.resume_metadata.email IS 'Email address of the volunteer (linking key)';
COMMENT ON COLUMN public.resume_metadata.volunteer_id IS 'Optional foreign key to volunteers table';
COMMENT ON COLUMN public.resume_metadata.resume_s3_url IS 'S3 URL of the original resume PDF';
COMMENT ON COLUMN public.resume_metadata.filename IS 'Original filename of the resume';
COMMENT ON COLUMN public.resume_metadata.processing_status IS 'Overall processing status: processing, completed, failed';
COMMENT ON COLUMN public.resume_metadata.extraction_status IS 'PDF extraction status: pending, success, failed';
COMMENT ON COLUMN public.resume_metadata.qdrant_point_id IS 'UUID of the corresponding point in Qdrant resumes collection';
COMMENT ON COLUMN public.resume_metadata.skills_extracted IS 'Array of skills extracted from the resume';
COMMENT ON COLUMN public.resume_metadata.languages_extracted IS 'Array of languages extracted from the resume';
COMMENT ON COLUMN public.resume_metadata.processing_time_ms IS 'Total processing time in milliseconds';
COMMENT ON COLUMN public.resume_metadata.error_message IS 'Error message if processing failed';
COMMENT ON COLUMN public.resume_metadata.error_type IS 'Type of error that occurred during processing';
