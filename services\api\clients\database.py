"""
Database client for Supabase operations.

This module provides functionality to interact with Supabase database
for storing and retrieving volunteer profile data using the Python client.
"""

import asyncio
from typing import Dict, Any, Optional, List
from uuid import UUID
from datetime import datetime
import json

from supabase import create_client, Client
from postgrest.exceptions import APIError

from common.settings import settings
from common.logging import get_logger
from common.models import ResumeMetadata

logger = get_logger(__name__)


class DatabaseClient:
    """
    Client for interacting with Supabase database.
    
    This client provides methods for storing, retrieving, and managing
    volunteer profile data using the Supabase Python client.
    """
    
    def __init__(self):
        """Initialize the database client."""
        self.client: Optional[Client] = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize the Supabase client."""
        if self._initialized:
            return
        
        try:
            # Use environment variables directly (settings object not loading properly)
            import os
            from dotenv import load_dotenv
            load_dotenv()

            supabase_url = os.getenv('SUPABASE_URL')
            supabase_key = os.getenv('SUPABASE_ANON_KEY')

            logger.info(f"Initializing Supabase client with URL: {supabase_url}")
            logger.info(f"Supabase key present: {bool(supabase_key)}")

            if not supabase_url:
                raise ValueError("SUPABASE_URL environment variable is not set")
            if not supabase_key:
                raise ValueError("SUPABASE_ANON_KEY environment variable is not set")

            # Create Supabase client
            self.client = create_client(supabase_url, supabase_key)
            
            self._initialized = True
            logger.info(
                "Supabase client initialized successfully",
                url=supabase_url
            )
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            raise
    
    async def ensure_tables_exist(self):
        """Ensure required tables exist in Supabase."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Check if volunteers table exists by trying to query it
            try:
                response = self.client.table("volunteers").select("id").limit(1).execute()
                logger.info("Volunteers table exists and is accessible")
            except APIError as e:
                if "relation" in str(e).lower() and "does not exist" in str(e).lower():
                    logger.warning("Volunteers table does not exist - will be created via Supabase dashboard or migrations")
                else:
                    logger.error(f"Error checking volunteers table: {e}")
            
            # Check processing_logs table
            try:
                response = self.client.table("processing_logs").select("id").limit(1).execute()
                logger.info("Processing logs table exists and is accessible")
            except APIError as e:
                if "relation" in str(e).lower() and "does not exist" in str(e).lower():
                    logger.warning("Processing logs table does not exist - will be created via Supabase dashboard or migrations")
                else:
                    logger.error(f"Error checking processing_logs table: {e}")
            
            logger.info("Database table check completed")
            
        except Exception as e:
            logger.error(f"Failed to check database tables: {e}")
            raise
    
    async def store_volunteer_profile(
        self,
        volunteer_id: UUID,
        email: str,
        profile_data: Dict[str, Any],
        source_type: str = "linkedin",
        vector_metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Store or update volunteer profile data using the new schema.

        Args:
            volunteer_id: UUID of the volunteer
            email: Email address of the volunteer
            profile_data: Dictionary containing profile information
            source_type: Source of the data (linkedin, resume, etc.)
            vector_metadata: Optional metadata for Qdrant vector database linking

        Returns:
            True if storage was successful
        """
        if not self._initialized:
            await self.initialize()

        try:
            # Extract key fields from profile data
            full_name = profile_data.get("full_name", "")
            headline = profile_data.get("headline")
            location = profile_data.get("location")
            about_summary = profile_data.get("about_summary")
            current_company = profile_data.get("current_company")
            current_position = profile_data.get("current_position")

            # Extract skills and languages from profile data
            skills = profile_data.get("skills", [])
            if not isinstance(skills, list):
                skills = []

            languages_spoken = profile_data.get("languages", [])
            if not isinstance(languages_spoken, list):
                languages_spoken = []

            # Use provided vector_metadata or extract from profile_data
            final_vector_metadata = vector_metadata or profile_data.get("qdrant_metadata", {})
            if not isinstance(final_vector_metadata, dict):
                final_vector_metadata = {}

            # Prepare volunteer data for new schema
            volunteer_data = {
                "id": str(volunteer_id),
                "email": email,
                "full_name": full_name,
                "headline": headline,
                "location": location,
                "about_summary": about_summary,
                "current_company": current_company,
                "current_position": current_position,
                "skills": skills,
                "vector_metadata": final_vector_metadata,
                "languages_spoken": languages_spoken,
                "source_types": [source_type],
                "updated_at": datetime.now().isoformat()
            }

            # Upsert volunteer record
            response = self.client.table("volunteers").upsert(
                volunteer_data,
                on_conflict="id"
            ).execute()

            if response.data:
                logger.info(
                    f"Successfully stored volunteer profile {volunteer_id}",
                    email=email,
                    source_type=source_type,
                    skills_count=len(skills),
                    languages_count=len(languages_spoken),
                    has_vector_metadata=bool(final_vector_metadata)
                )
                return True
            else:
                logger.error(f"Failed to store volunteer profile {volunteer_id}: No data returned")
                return False

        except Exception as e:
            logger.error(
                f"Failed to store volunteer profile {volunteer_id}: {e}",
                email=email,
                source_type=source_type
            )
            return False
    
    async def get_volunteer_by_id(self, volunteer_id: UUID) -> Optional[Dict[str, Any]]:
        """
        Get volunteer profile by ID.
        
        Args:
            volunteer_id: UUID of the volunteer
            
        Returns:
            Dictionary containing volunteer data, or None if not found
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            response = self.client.table("volunteers").select("*").eq("id", str(volunteer_id)).execute()
            
            if response.data:
                return response.data[0]
            return None
                
        except Exception as e:
            logger.error(f"Failed to get volunteer by ID {volunteer_id}: {e}")
            return None

    async def store_volunteer_profile_with_multi_vector_metadata(
        self,
        volunteer_id: UUID,
        email: str,
        profile_data: Dict[str, Any],
        multi_vector_metadata: Dict[str, Any],
        source_type: str = "linkedin"
    ) -> bool:
        """
        Store or update volunteer profile data with multi-vector metadata.

        Args:
            volunteer_id: UUID of the volunteer
            email: Email address of the volunteer
            profile_data: Dictionary containing profile information
            multi_vector_metadata: Metadata for multi-vector Qdrant database linking
            source_type: Source of the data (linkedin, resume, etc.)

        Returns:
            True if storage was successful
        """
        return await self.store_volunteer_profile(
            volunteer_id=volunteer_id,
            email=email,
            profile_data=profile_data,
            source_type=source_type,
            vector_metadata=multi_vector_metadata
        )

    async def get_volunteer_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """
        Get volunteer profile by email.

        Args:
            email: Email address of the volunteer

        Returns:
            Dictionary containing volunteer data, or None if not found
        """
        if not self._initialized:
            await self.initialize()

        try:
            response = self.client.table("volunteers").select("*").eq("email", email).execute()

            if response.data:
                return response.data[0]
            return None

        except Exception as e:
            logger.error(f"Failed to get volunteer by email {email}: {e}")
            return None

    async def get_volunteer_with_qdrant_lookup(
        self,
        volunteer_id: UUID,
        include_vector_data: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        Get volunteer data with optional Qdrant vector data lookup.

        Args:
            volunteer_id: UUID of the volunteer
            include_vector_data: Whether to fetch complete profile data from Qdrant

        Returns:
            Dictionary containing combined volunteer data, or None if not found
        """
        if not self._initialized:
            await self.initialize()

        try:
            # Get Supabase data
            volunteer_data = await self.get_volunteer_by_id(volunteer_id)
            if not volunteer_data:
                return None

            # If vector data is requested and vector metadata exists
            vector_metadata = volunteer_data.get("vector_metadata", {})

            if include_vector_data and vector_metadata:
                try:
                    from services.api.clients.qdrant import get_qdrant_client
                    qdrant_client = get_qdrant_client()

                    # Get vector data from Qdrant
                    vector_data = await qdrant_client.get_point(str(volunteer_id))
                    if vector_data:
                        volunteer_data["vector_data"] = vector_data
                        logger.debug(f"Retrieved multi-vector data for volunteer {volunteer_id}")

                except Exception as e:
                    logger.warning(f"Failed to retrieve vector data for volunteer {volunteer_id}: {e}")
                    # Continue without vector data

            return volunteer_data

        except Exception as e:
            logger.error(f"Failed to get volunteer with Qdrant lookup {volunteer_id}: {e}")
            return None
    
    async def search_volunteers(
        self,
        query: Optional[str] = None,
        skills: Optional[List[str]] = None,
        location: Optional[str] = None,
        company: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Search volunteers with various filters.
        
        Args:
            query: Text search query
            skills: List of skills to filter by
            location: Location filter
            company: Company filter
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of volunteer dictionaries
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Start with base query
            supabase_query = self.client.table("volunteers").select("*")
            
            # Apply filters
            if query:
                # Search in multiple text fields
                supabase_query = supabase_query.or_(
                    f"full_name.ilike.%{query}%,"
                    f"headline.ilike.%{query}%,"
                    f"about_summary.ilike.%{query}%"
                )
            
            if location:
                supabase_query = supabase_query.ilike("location", f"%{location}%")
            
            if company:
                supabase_query = supabase_query.ilike("current_company", f"%{company}%")
            
            # Apply pagination
            supabase_query = supabase_query.range(offset, offset + limit - 1)
            
            # Execute query
            response = supabase_query.execute()
            
            return response.data or []
                
        except Exception as e:
            logger.error(f"Failed to search volunteers: {e}")
            return []
    
    async def create_processing_log(
        self,
        volunteer_id: UUID,
        source_type: str,
        status: str = "processing",
        source_url: Optional[str] = None,
        error_message: Optional[str] = None,
        processing_time_ms: Optional[int] = None
    ) -> bool:
        """
        Create a processing log entry.
        
        Args:
            volunteer_id: UUID of the volunteer
            source_type: Type of processing (linkedin, resume, etc.)
            status: Processing status
            source_url: Source URL if applicable
            error_message: Error message if failed
            processing_time_ms: Processing time in milliseconds
            
        Returns:
            True if creation was successful
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            log_data = {
                "volunteer_id": str(volunteer_id),
                "source_type": source_type,
                "status": status,
                "source_url": source_url,
                "error_message": error_message,
                "processing_time_ms": processing_time_ms,
                "created_at": datetime.now().isoformat()
            }
            
            if status in ["completed", "failed"]:
                log_data["completed_at"] = datetime.now().isoformat()
            
            response = self.client.table("processing_logs").insert(log_data).execute()
            
            if response.data:
                logger.info(
                    f"Created processing log for volunteer {volunteer_id}",
                    source_type=source_type,
                    status=status
                )
                return True
            else:
                logger.error(f"Failed to create processing log: No data returned")
                return False
                
        except Exception as e:
            logger.error(
                f"Failed to create processing log for volunteer {volunteer_id}: {e}",
                source_type=source_type,
                status=status
            )
            return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """
        Get database statistics.
        
        Returns:
            Dictionary containing statistics
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Get volunteer count
            volunteers_response = self.client.table("volunteers").select("id", count="exact").execute()
            volunteer_count = volunteers_response.count or 0
            
            # Get processing logs count
            logs_response = self.client.table("processing_logs").select("id", count="exact").execute()
            logs_count = logs_response.count or 0
            
            # Get recent activity (last 24 hours)
            recent_cutoff = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).isoformat()
            recent_response = self.client.table("volunteers").select("id", count="exact").gte("updated_at", recent_cutoff).execute()
            recent_updates = recent_response.count or 0
            
            return {
                "total_volunteers": volunteer_count,
                "total_processing_logs": logs_count,
                "recent_updates_24h": recent_updates,
                "database_type": "supabase",
                "timestamp": datetime.now().isoformat()
            }
                
        except Exception as e:
            logger.error(f"Failed to get Supabase database statistics: {e}")
            return {
                "error": str(e),
                "database_type": "supabase",
                "timestamp": datetime.now().isoformat()
            }

    async def store_resume_metadata(
        self,
        email: str,
        resume_s3_url: str,
        filename: Optional[str] = None,
        qdrant_point_id: Optional[UUID] = None,
        processing_status: str = "processing",
        extraction_status: str = "pending"
    ) -> Optional[UUID]:
        """
        Store initial resume metadata record.

        Args:
            email: Email address of the volunteer
            resume_s3_url: S3 URL of the resume PDF
            filename: Optional original filename
            qdrant_point_id: Optional UUID of the Qdrant point
            processing_status: Initial processing status
            extraction_status: Initial extraction status

        Returns:
            UUID of the created record, or None if failed
        """
        if not self._initialized:
            await self.initialize()

        try:
            # Check if volunteer exists and get volunteer_id
            volunteer_id = None
            volunteer_response = self.client.table("volunteers").select("id").eq("email", email).execute()
            if volunteer_response.data:
                volunteer_id = volunteer_response.data[0]["id"]

            # Prepare resume metadata
            metadata = {
                "email": email,
                "volunteer_id": volunteer_id,
                "resume_s3_url": resume_s3_url,
                "filename": filename,
                "processing_status": processing_status,
                "extraction_status": extraction_status,
                "qdrant_point_id": str(qdrant_point_id) if qdrant_point_id else None,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

            # Insert resume metadata
            response = self.client.table("resume_metadata").insert(metadata).execute()

            if response.data:
                record_id = response.data[0]["id"]
                logger.info(
                    f"Successfully stored resume metadata for {email}",
                    record_id=record_id,
                    volunteer_id=volunteer_id,
                    filename=filename
                )
                return UUID(record_id)
            else:
                logger.error(f"Failed to store resume metadata for {email}: No data returned")
                return None

        except Exception as e:
            logger.error(
                f"Failed to store resume metadata for {email}: {e}",
                resume_s3_url=resume_s3_url,
                filename=filename
            )
            return None

    async def update_resume_metadata(
        self,
        record_id: UUID,
        profile_data: Optional[Dict[str, Any]] = None,
        processing_status: Optional[str] = None,
        extraction_status: Optional[str] = None,
        processing_time_ms: Optional[int] = None,
        error_message: Optional[str] = None,
        error_type: Optional[str] = None
    ) -> bool:
        """
        Update resume metadata record with processing results.

        Args:
            record_id: UUID of the resume metadata record
            profile_data: Optional extracted profile data
            processing_status: Updated processing status
            extraction_status: Updated extraction status
            processing_time_ms: Processing time in milliseconds
            error_message: Error message if processing failed
            error_type: Type of error that occurred

        Returns:
            True if update was successful
        """
        if not self._initialized:
            await self.initialize()

        try:
            # Prepare update data
            update_data = {
                "updated_at": datetime.now().isoformat()
            }

            if processing_status:
                update_data["processing_status"] = processing_status

            if extraction_status:
                update_data["extraction_status"] = extraction_status

            if processing_time_ms is not None:
                update_data["processing_time_ms"] = processing_time_ms

            if error_message:
                update_data["error_message"] = error_message

            if error_type:
                update_data["error_type"] = error_type

            if processing_status == "completed":
                update_data["processed_at"] = datetime.now().isoformat()

            # Extract data from profile if provided
            if profile_data:
                update_data.update({
                    "skills_extracted": profile_data.get("skills", []),
                    "languages_extracted": profile_data.get("languages", []),
                    "full_name_extracted": profile_data.get("full_name"),
                    "location_extracted": profile_data.get("location"),
                    "phone_extracted": profile_data.get("phone"),
                    "summary_extracted": profile_data.get("summary"),
                    "work_experience_count": len(profile_data.get("work_experience", [])),
                    "education_count": len(profile_data.get("education", [])),
                    "certifications_count": len(profile_data.get("certifications", [])),
                    "volunteer_experience_count": len(profile_data.get("volunteer_experience", [])),
                    "projects_count": len(profile_data.get("projects", []))
                })

            # Update the record
            response = self.client.table("resume_metadata").update(update_data).eq("id", str(record_id)).execute()

            if response.data:
                logger.info(
                    f"Successfully updated resume metadata {record_id}",
                    processing_status=processing_status,
                    extraction_status=extraction_status,
                    has_profile_data=bool(profile_data)
                )
                return True
            else:
                logger.error(f"Failed to update resume metadata {record_id}: No data returned")
                return False

        except Exception as e:
            logger.error(
                f"Failed to update resume metadata {record_id}: {e}",
                processing_status=processing_status,
                extraction_status=extraction_status
            )
            return False

    async def get_resume_metadata_by_email(self, email: str) -> List[Dict[str, Any]]:
        """
        Get all resume metadata records for a given email.

        Args:
            email: Email address to search for

        Returns:
            List of resume metadata records
        """
        if not self._initialized:
            await self.initialize()

        try:
            response = self.client.table("resume_metadata").select("*").eq("email", email).execute()
            return response.data or []

        except Exception as e:
            logger.error(f"Failed to get resume metadata for {email}: {e}")
            return []

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the Supabase database.
        
        Returns:
            Dictionary containing health check results
        """
        try:
            if not self._initialized:
                await self.initialize()

            # Test basic connectivity by checking if we can make any query
            # Just check if the client is working - don't worry about specific tables
            response = None
            connection_working = True

            # Check if we can access our tables
            volunteers_accessible = False
            logs_accessible = False
            
            try:
                self.client.table("volunteers").select("id").limit(1).execute()
                volunteers_accessible = True
            except:
                pass
            
            try:
                self.client.table("processing_logs").select("id").limit(1).execute()
                logs_accessible = True
            except:
                pass
            
            return {
                "status": "healthy",
                "connected": True,
                "database_type": "supabase",
                "volunteers_table_accessible": volunteers_accessible,
                "processing_logs_table_accessible": logs_accessible,
                "client_initialized": self._initialized
            }
                
        except Exception as e:
            logger.error(f"Supabase database health check failed: {e}")
            return {
                "status": "unhealthy",
                "connected": False,
                "database_type": "supabase",
                "error": str(e)
            }
    
    async def close(self):
        """Close the database connection."""
        if self.client:
            # Supabase client doesn't need explicit closing
            self._initialized = False
            logger.info("Supabase client connection closed")


# Global client instance
_database_client = None


async def get_database_client() -> DatabaseClient:
    """
    Get the global Supabase database client instance.
    
    Returns:
        DatabaseClient instance
    """
    global _database_client
    
    if _database_client is None:
        _database_client = DatabaseClient()
        await _database_client.initialize()
        await _database_client.ensure_tables_exist()
    
    return _database_client


async def database_health_check() -> Dict[str, Any]:
    """
    Perform a health check on the database connection.
    
    Returns:
        Dictionary containing health check results
    """
    try:
        client = await get_database_client()
        return await client.health_check()
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "unhealthy",
            "connected": False,
            "database_type": "supabase",
            "error": str(e)
        } 