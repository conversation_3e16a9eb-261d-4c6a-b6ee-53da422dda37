"""
Middleware for the skill extractor API service.

This module contains custom middleware for JWT authentication,
rate limiting, and other cross-cutting concerns.
"""

import os
import time
from typing import Optional, Set
from urllib.parse import urlparse

import jwt
from fastapi import Request, HTTPException, status
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware

from common.settings import settings
from common.logging import get_logger

logger = get_logger(__name__)


class JWTMiddleware(BaseHTTPMiddleware):
    """
    JWT authentication middleware.
    
    Validates JWT tokens for protected endpoints and extracts user information.
    Skips authentication for public endpoints like health checks and metrics.
    """
    
    # Endpoints that don't require authentication
    PUBLIC_ENDPOINTS: Set[str] = {
        "/",
        "/health",
        "/healthz",
        "/metrics",
        "/docs",
        "/redoc",
        "/openapi.json"
    }
    
    def __init__(self, app, public_endpoints: Optional[Set[str]] = None):
        super().__init__(app)
        if public_endpoints:
            self.PUBLIC_ENDPOINTS.update(public_endpoints)
    
    async def dispatch(self, request: Request, call_next):
        """Process the request and validate JW<PERSON> if required."""
        
        # Skip authentication for public endpoints
        if self._is_public_endpoint(request.url.path):
            return await call_next(request)
        
        # Extract and validate JWT token
        try:
            token = self._extract_token(request)
            if not token:
                return self._unauthorized_response("Missing authentication token")
            
            # Validate and decode token
            payload = self._validate_token(token)
            
            # Add user information to request state
            request.state.user = payload
            request.state.user_id = payload.get("sub")
            request.state.user_roles = payload.get("roles", [])
            
            # Log successful authentication
            logger.debug(
                "JWT authentication successful",
                user_id=payload.get("sub"),
                roles=payload.get("roles", []),
                endpoint=request.url.path
            )
            
            return await call_next(request)
            
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired", endpoint=request.url.path)
            return self._unauthorized_response("Token has expired")
            
        except jwt.InvalidTokenError as e:
            logger.warning(
                "Invalid JWT token",
                error=str(e),
                endpoint=request.url.path
            )
            return self._unauthorized_response("Invalid authentication token")
            
        except Exception as e:
            logger.error(
                "JWT authentication error",
                error=str(e),
                endpoint=request.url.path,
                exc_info=True
            )
            return self._unauthorized_response("Authentication failed")
    
    def _is_public_endpoint(self, path: str) -> bool:
        """Check if the endpoint is public (doesn't require authentication)."""
        
        # Remove query parameters and fragments
        clean_path = path.split("?")[0].split("#")[0]
        
        # Check exact matches
        if clean_path in self.PUBLIC_ENDPOINTS:
            return True
        
        # Check path prefixes for public endpoints
        public_prefixes = ["/health", "/docs", "/redoc"]
        for prefix in public_prefixes:
            if clean_path.startswith(prefix):
                return True
        
        return False
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """Extract JWT token from Authorization header."""
        
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return None
        
        # Check for Bearer token format
        if not auth_header.startswith("Bearer "):
            return None
        
        return auth_header[7:]  # Remove "Bearer " prefix
    
    def _validate_token(self, token: str) -> dict:
        """
        Validate and decode JWT token.

        Args:
            token: JWT token string

        Returns:
            Decoded token payload

        Raises:
            jwt.InvalidTokenError: If token is invalid
        """

        # Try production JWT validation first
        if settings.security.jwt_public_key:
            try:
                payload = jwt.decode(
                    token,
                    settings.security.jwt_public_key,
                    algorithms=[settings.security.jwt_algorithm],
                    audience=settings.security.jwt_audience,
                    issuer=settings.security.jwt_issuer,
                    options={
                        "verify_signature": True,
                        "verify_exp": True,
                        "verify_aud": settings.security.jwt_audience is not None,
                        "verify_iss": settings.security.jwt_issuer is not None,
                    }
                )
                return payload
            except jwt.InvalidTokenError:
                # Fall through to development token validation
                pass

        # Try development token validation (HS256 with simple secret)
        if settings.environment in ["development", "dev", "local"]:
            try:
                jwt_secret = os.getenv("JWT_SECRET", "development-secret-key-not-for-production")
                payload = jwt.decode(
                    token,
                    jwt_secret,
                    algorithms=["HS256"],
                    options={
                        "verify_signature": True,
                        "verify_exp": True,
                        "verify_aud": False,  # Skip audience verification for dev tokens
                        "verify_iss": False,  # Skip issuer verification for dev tokens
                    }
                )

                logger.debug("Development JWT token validated successfully")
                return payload

            except jwt.InvalidTokenError as e:
                logger.debug(f"Development token validation failed: {e}")

        # If both validations fail, raise the error
        raise jwt.InvalidTokenError("Token validation failed")
    
    def _unauthorized_response(self, message: str) -> JSONResponse:
        """Create an unauthorized response."""
        
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "error": "Unauthorized",
                "message": message,
                "type": "authentication_error"
            },
            headers={"WWW-Authenticate": "Bearer"}
        )


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware.
    
    Implements simple in-memory rate limiting based on client IP.
    For production use, consider using Redis or other distributed storage.
    """
    
    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.client_requests = {}  # In-memory store for rate limiting
        self.window_size = 60  # 1 minute window
    
    async def dispatch(self, request: Request, call_next):
        """Apply rate limiting based on client IP."""
        
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Check rate limit
        if self._is_rate_limited(client_ip):
            logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                endpoint=request.url.path
            )
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "error": "Too Many Requests",
                    "message": f"Rate limit exceeded. Maximum {self.requests_per_minute} requests per minute.",
                    "retry_after": 60
                },
                headers={"Retry-After": "60"}
            )
        
        # Record request
        self._record_request(client_ip)
        
        return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        
        # Check for forwarded headers (for reverse proxy setups)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        return request.client.host if request.client else "unknown"
    
    def _is_rate_limited(self, client_ip: str) -> bool:
        """Check if client has exceeded rate limit."""
        
        current_time = time.time()
        
        # Clean up old entries
        self._cleanup_old_entries(current_time)
        
        # Check current request count
        if client_ip not in self.client_requests:
            return False
        
        request_count = len(self.client_requests[client_ip])
        return request_count >= self.requests_per_minute
    
    def _record_request(self, client_ip: str) -> None:
        """Record a request for rate limiting."""
        
        current_time = time.time()
        
        if client_ip not in self.client_requests:
            self.client_requests[client_ip] = []
        
        self.client_requests[client_ip].append(current_time)
    
    def _cleanup_old_entries(self, current_time: float) -> None:
        """Remove entries older than the window size."""
        
        cutoff_time = current_time - self.window_size
        
        for client_ip in list(self.client_requests.keys()):
            # Filter out old requests
            self.client_requests[client_ip] = [
                timestamp for timestamp in self.client_requests[client_ip]
                if timestamp > cutoff_time
            ]
            
            # Remove empty entries
            if not self.client_requests[client_ip]:
                del self.client_requests[client_ip]


def get_current_user(request: Request) -> dict:
    """
    Dependency to get current authenticated user from request state.
    
    Args:
        request: FastAPI request object
        
    Returns:
        User information from JWT token
        
    Raises:
        HTTPException: If user is not authenticated
    """
    
    if not hasattr(request.state, 'user'):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    return request.state.user


def require_role(required_role: str):
    """
    Dependency factory to require specific user roles.
    
    Args:
        required_role: Role required to access the endpoint
        
    Returns:
        Dependency function
    """
    
    def role_checker(request: Request) -> dict:
        user = get_current_user(request)
        user_roles = user.get("roles", [])
        
        if required_role not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role '{required_role}' required"
            )
        
        return user
    
    return role_checker 