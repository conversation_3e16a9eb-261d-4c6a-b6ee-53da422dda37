#!/usr/bin/env python3
"""
Integration test script to verify LinkedIn processing functionality.

This script tests the LinkedIn profile processing to ensure it's still working
correctly after recent changes to the resume processing system.
"""

import asyncio
import os
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from common.logging import get_logger
from common.utils import volunteer_id_from_email
from services.api.clients.browser_use import get_browser_use_client, extract_linkedin_profile
from services.api.clients.dual_storage import get_dual_storage_service
from services.api.routes.ingest import process_linkedin_profile

logger = get_logger(__name__)


class LinkedInTestRunner:
    """Test runner for LinkedIn processing functionality."""
    
    def __init__(self):
        self.test_profile_url = "https://www.linkedin.com/in/yashkhivasara/"
        self.test_email = "<EMAIL>"
        self.test_volunteer_id = volunteer_id_from_email(self.test_email)
    
    def print_header(self):
        """Print test header."""
        print("=" * 80)
        print("🔗 LINKEDIN PROCESSING FUNCTIONALITY TESTS")
        print("=" * 80)
        print(f"Test Profile URL: {self.test_profile_url}")
        print(f"Test Email: {self.test_email}")
        print(f"Test Volunteer ID: {self.test_volunteer_id}")
        print()
    
    async def test_environment_configuration(self):
        """Test LinkedIn-specific environment configuration."""
        print("⚙️  Testing LinkedIn Environment Configuration...")
        
        required_vars = [
            'LINKEDIN_EMAIL',
            'LINKEDIN_PASSWORD',
            'AZURE_OPENAI_ENDPOINT',
            'AZURE_OPENAI_KEY',
            'AZURE_OPENAI_DEPLOYMENT_NAME'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"   ❌ Missing LinkedIn environment variables: {', '.join(missing_vars)}")
            print("   💡 LinkedIn extraction requires browser automation credentials")
            return False
        else:
            print("   ✅ All LinkedIn environment variables are set")
            return True
    
    async def test_browser_client_initialization(self):
        """Test browser client initialization."""
        print("\n🌐 Testing Browser Client Initialization...")
        
        try:
            browser_client = get_browser_use_client()
            print("   ✅ Browser client initialized successfully")
            return True
        except Exception as e:
            print(f"   ❌ Browser client initialization failed: {e}")
            return False
    
    async def test_dual_storage_service_initialization(self):
        """Test dual storage service initialization."""
        print("\n💾 Testing Dual Storage Service Initialization...")
        
        try:
            dual_storage = await get_dual_storage_service()
            print("   ✅ Dual storage service initialized successfully")
            return True
        except Exception as e:
            print(f"   ❌ Dual storage service initialization failed: {e}")
            return False
    
    async def test_linkedin_extraction_only(self):
        """Test LinkedIn profile extraction without storage."""
        print("\n🔍 Testing LinkedIn Profile Extraction (No Storage)...")
        
        try:
            print(f"   Extracting profile from: {self.test_profile_url}")
            print("   ⚠️  This may take 30-60 seconds...")
            
            start_time = time.time()
            
            # Extract profile data
            profile_data = await extract_linkedin_profile(self.test_profile_url)
            
            end_time = time.time()
            extraction_time = end_time - start_time
            
            if profile_data:
                print("   ✅ LinkedIn profile extraction succeeded!")
                print(f"   ⏱️  Extraction time: {extraction_time:.2f} seconds")
                print(f"   👤 Full name: {profile_data.get('full_name', 'N/A')}")
                print(f"   💼 Headline: {profile_data.get('headline', 'N/A')}")
                print(f"   📍 Location: {profile_data.get('location', 'N/A')}")
                print(f"   🎯 Skills count: {len(profile_data.get('skills', []))}")
                return True, profile_data
            else:
                print("   ❌ LinkedIn profile extraction failed!")
                print(f"   ⏱️  Attempted for: {extraction_time:.2f} seconds")
                return False, None
                
        except Exception as e:
            print(f"   ❌ Exception during LinkedIn extraction: {e}")
            return False, None
    
    async def test_full_linkedin_processing(self):
        """Test full LinkedIn processing pipeline."""
        print("\n🔄 Testing Full LinkedIn Processing Pipeline...")
        
        try:
            print(f"   Processing LinkedIn profile for volunteer: {self.test_volunteer_id}")
            print("   ⚠️  This may take 60-120 seconds...")
            
            start_time = time.time()
            
            # Process LinkedIn profile (includes extraction + dual storage)
            success = await process_linkedin_profile(
                volunteer_id=self.test_volunteer_id,
                linkedin_url=self.test_profile_url,
                email=self.test_email
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if success:
                print("   ✅ Full LinkedIn processing succeeded!")
                print(f"   ⏱️  Total processing time: {processing_time:.2f} seconds")
                print(f"   💾 Data stored in both Supabase and Qdrant")
                return True
            else:
                print("   ❌ Full LinkedIn processing failed!")
                print(f"   ⏱️  Attempted for: {processing_time:.2f} seconds")
                return False
                
        except Exception as e:
            print(f"   ❌ Exception during full LinkedIn processing: {e}")
            return False
    
    async def test_linkedin_api_endpoint_structure(self):
        """Test LinkedIn API endpoint structure (without actual HTTP calls)."""
        print("\n📡 Testing LinkedIn API Endpoint Structure...")
        
        try:
            # Import the endpoint function to verify it exists
            from services.api.routes.ingest import ingest_linkedin_profile
            print("   ✅ LinkedIn ingestion endpoint exists")
            
            # Check if the minimal endpoint exists
            from services.api.routes.ingest import ingest_linkedin_profile_minimal
            print("   ✅ LinkedIn minimal endpoint exists")
            
            # Check required models
            from common.models import LinkedInIngestRequest
            print("   ✅ LinkedIn request model exists")
            
            return True
            
        except ImportError as e:
            print(f"   ❌ LinkedIn endpoint structure issue: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all LinkedIn functionality tests."""
        self.print_header()
        
        results = {}
        
        # Test environment
        results['environment'] = await self.test_environment_configuration()
        
        # Test API structure
        results['api_structure'] = await self.test_linkedin_api_endpoint_structure()
        
        # Test service initialization
        results['browser_client'] = await self.test_browser_client_initialization()
        results['dual_storage'] = await self.test_dual_storage_service_initialization()
        
        # Only run extraction tests if environment is configured
        if results['environment']:
            # Test extraction only
            extraction_success, profile_data = await self.test_linkedin_extraction_only()
            results['extraction'] = extraction_success
            
            # Test full processing if extraction worked
            if extraction_success:
                results['full_processing'] = await self.test_full_linkedin_processing()
            else:
                print("\n⏭️  Skipping full processing test (extraction failed)")
                results['full_processing'] = False
        else:
            print("\n⏭️  Skipping LinkedIn extraction tests (environment not configured)")
            results['extraction'] = None
            results['full_processing'] = None
        
        # Print summary
        self.print_summary(results)
    
    def print_summary(self, results: Dict[str, Any]):
        """Print test summary."""
        print("\n" + "=" * 80)
        print("📊 LINKEDIN FUNCTIONALITY TEST SUMMARY")
        print("=" * 80)
        
        # Environment and structure
        print(f"Environment Configuration: {'✅ OK' if results['environment'] else '❌ MISSING'}")
        print(f"API Endpoint Structure: {'✅ OK' if results['api_structure'] else '❌ FAILED'}")
        
        # Service initialization
        print(f"Browser Client Init: {'✅ OK' if results['browser_client'] else '❌ FAILED'}")
        print(f"Dual Storage Init: {'✅ OK' if results['dual_storage'] else '❌ FAILED'}")
        
        # Processing tests
        if results['extraction'] is not None:
            print(f"LinkedIn Extraction: {'✅ PASSED' if results['extraction'] else '❌ FAILED'}")
        else:
            print(f"LinkedIn Extraction: ⏭️  SKIPPED")
        
        if results['full_processing'] is not None:
            print(f"Full Processing: {'✅ PASSED' if results['full_processing'] else '❌ FAILED'}")
        else:
            print(f"Full Processing: ⏭️  SKIPPED")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        
        if results['api_structure'] and results['browser_client'] and results['dual_storage']:
            print("✅ LinkedIn processing infrastructure is intact")
            
            if results['environment']:
                if results['extraction'] and results['full_processing']:
                    print("✅ LinkedIn processing is fully functional")
                elif results['extraction']:
                    print("⚠️  LinkedIn extraction works, but full processing had issues")
                else:
                    print("❌ LinkedIn processing has functional issues")
            else:
                print("⚠️  LinkedIn processing infrastructure is ready, but environment needs configuration")
        else:
            print("❌ LinkedIn processing infrastructure has issues")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if not results['environment']:
            print("1. Configure LinkedIn credentials in .env file:")
            print("   - LINKEDIN_EMAIL=<EMAIL>")
            print("   - LINKEDIN_PASSWORD=your_password")
            print("   - AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/")
            print("   - AZURE_OPENAI_KEY=your_api_key")
            print("   - AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name")
        
        if results['environment'] and results['extraction']:
            print("1. LinkedIn functionality is working correctly")
            print("2. Both resume and LinkedIn processing are operational")
        
        if not results['api_structure']:
            print("1. Check LinkedIn API endpoint imports and structure")


async def main():
    """Main test function."""
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Run tests
    test_runner = LinkedInTestRunner()
    await test_runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
