#!/usr/bin/env python3
"""
Test cases for the vector-only resume processing functionality.

This module tests the simplified vector-only approach for resume processing
that stores complete resume content as single vector embeddings without
structured parsing.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import Test<PERSON>lient
from fastapi import status

# Mock the dependencies before importing the app
with patch('services.api.clients.resume_service.process_resume_vector_only') as mock_process_resume_vector_only:
    from services.api.main import app

client = TestClient(app)


class TestResumeVectorOnlyAPI:
    """Test cases for the vector-only resume processing API."""
    
    def test_resume_simple_endpoint_exists(self):
        """Test that the resume-simple endpoint exists and requires authentication."""
        response = client.post("/api/ingest/resume-simple", json={
            "email": "<EMAIL>",
            "resume_s3_url": "https://example-bucket.s3.amazonaws.com/test.pdf"
        })
        
        # Should return 401 or 403 due to missing authentication
        assert response.status_code in [401, 403]
    
    @patch('services.api.clients.resume_service.process_resume_vector_only')
    @patch('services.api.middleware.require_role')
    def test_vector_only_processing_success(self, mock_require_role, mock_process_resume_vector_only):
        """Test successful vector-only resume processing."""
        # Mock authentication
        mock_require_role.return_value = lambda: {"sub": "test-user"}
        
        # Mock successful vector-only processing
        from services.api.clients.resume_service import ResumeVectorOnlyResult
        
        mock_result = ResumeVectorOnlyResult(
            success=True,
            email="<EMAIL>",
            point_id="test-point-id-123",
            processing_time=1.5,
            text_length=2500
        )
        
        mock_process_resume_vector_only.return_value = mock_result
        
        # Make the request
        response = client.post("/api/ingest/resume-simple", json={
            "email": "<EMAIL>",
            "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/test.pdf",
            "filename": "test-resume.pdf"
        })
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert "Resume vector processing completed successfully" in data["message"]
        assert "2500 characters" in data["message"]
        assert "1.50 seconds" in data["message"]
        assert "volunteer_id" in data
        
        # Verify the service was called correctly
        mock_process_resume_vector_only.assert_called_once_with(
            email="<EMAIL>",
            resume_s3_url="https://skill-assessment-test.s3.amazonaws.com/test.pdf",
            filename="test-resume.pdf"
        )
    
    @patch('services.api.clients.resume_service.process_resume_vector_only')
    @patch('services.api.middleware.require_role')
    def test_vector_only_processing_failure(self, mock_require_role, mock_process_resume_vector_only):
        """Test vector-only processing failure handling."""
        # Mock authentication
        mock_require_role.return_value = lambda: {"sub": "test-user"}
        
        # Mock failed processing
        from services.api.clients.resume_service import ResumeVectorOnlyResult
        
        mock_result = ResumeVectorOnlyResult(
            success=False,
            email="<EMAIL>",
            point_id="test-point-id-123",
            processing_time=0.5,
            error_type="extraction_error",
            error_message="Insufficient text extracted from PDF"
        )
        
        mock_process_resume_vector_only.return_value = mock_result
        
        # Make the request
        response = client.post("/api/ingest/resume-simple", json={
            "email": "<EMAIL>",
            "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/invalid.pdf"
        })
        
        # Verify error response
        assert response.status_code == 422  # extraction_error maps to 422
        data = response.json()
        assert data["detail"]["error"] == "extraction_error"
        assert data["detail"]["message"] == "Insufficient text extracted from PDF"
        assert data["detail"]["email"] == "<EMAIL>"
        assert data["detail"]["processing_approach"] == "vector_only"
    
    @patch('services.api.clients.resume_service.process_resume_vector_only')
    @patch('services.api.middleware.require_role')
    def test_vector_only_error_types(self, mock_require_role, mock_process_resume_vector_only):
        """Test different error types in vector-only processing."""
        # Mock authentication
        mock_require_role.return_value = lambda: {"sub": "test-user"}
        
        from services.api.clients.resume_service import ResumeVectorOnlyResult
        
        # Test different error types and their expected status codes
        error_test_cases = [
            ("extraction_error", 422),
            ("vector_error", 500),
            ("storage_error", 500),
            ("initialization_error", 500),
            ("unexpected_error", 500)
        ]
        
        for error_type, expected_status in error_test_cases:
            mock_result = ResumeVectorOnlyResult(
                success=False,
                email="<EMAIL>",
                point_id="test-point-id-123",
                processing_time=0.5,
                error_type=error_type,
                error_message=f"Test {error_type}"
            )
            
            mock_process_resume_vector_only.return_value = mock_result
            
            response = client.post("/api/ingest/resume-simple", json={
                "email": "<EMAIL>",
                "resume_s3_url": "https://skill-assessment-test.s3.amazonaws.com/test.pdf"
            })
            
            assert response.status_code == expected_status, f"Error type {error_type} should return status {expected_status}"
            data = response.json()
            assert data["detail"]["error"] == error_type
            assert data["detail"]["processing_approach"] == "vector_only"


@pytest.mark.asyncio
class TestResumeVectorOnlyService:
    """Test cases for the vector-only resume processing service."""
    
    async def test_service_initialization(self):
        """Test that the vector-only service initializes correctly."""
        from services.api.clients.resume_service import ResumeVectorOnlyService
        
        service = ResumeVectorOnlyService()
        
        # Should not be initialized yet
        assert not service._initialized
        
        # Mock the dependencies to avoid actual initialization
        with patch('services.api.clients.vector_service.VectorGenerationService') as mock_vector_service, \
             patch('services.api.clients.qdrant.get_qdrant_resumes_client') as mock_qdrant_client:
            
            mock_vector_instance = AsyncMock()
            mock_vector_service.return_value = mock_vector_instance
            
            mock_qdrant_instance = AsyncMock()
            mock_qdrant_instance.ensure_collection_exists.return_value = True
            mock_qdrant_client.return_value = mock_qdrant_instance
            
            await service.initialize()
            
            # Should be initialized now
            assert service._initialized
            mock_vector_instance.initialize.assert_called_once()
            mock_qdrant_instance.ensure_collection_exists.assert_called_once()
    
    async def test_point_id_generation(self):
        """Test deterministic point ID generation from email."""
        from services.api.clients.resume_service import ResumeVectorOnlyService
        
        service = ResumeVectorOnlyService()
        
        # Same email should generate same point ID
        email = "<EMAIL>"
        point_id_1 = service._generate_point_id_from_email(email)
        point_id_2 = service._generate_point_id_from_email(email)
        
        assert point_id_1 == point_id_2
        assert len(point_id_1) == 36  # UUID format
        
        # Different emails should generate different point IDs
        different_email = "<EMAIL>"
        different_point_id = service._generate_point_id_from_email(different_email)
        
        assert point_id_1 != different_point_id
    
    async def test_vector_only_processing_flow(self):
        """Test the complete vector-only processing flow."""
        from services.api.clients.resume_service import ResumeVectorOnlyService
        
        service = ResumeVectorOnlyService()
        
        # Mock all dependencies
        with patch('services.api.clients.pdf_processor.PDFProcessor') as mock_pdf_processor, \
             patch('services.api.clients.vector_service.VectorGenerationService') as mock_vector_service, \
             patch('services.api.clients.qdrant.get_qdrant_resumes_client') as mock_qdrant_client:
            
            # Mock PDF processor
            mock_pdf_instance = AsyncMock()
            mock_pdf_instance.process_resume_pdf.return_value = "Sample resume text content with sufficient length to pass validation checks."
            mock_pdf_processor.return_value.__aenter__.return_value = mock_pdf_instance
            
            # Mock vector service
            mock_vector_instance = AsyncMock()
            mock_vector_instance.generate_embedding_from_text.return_value = [0.1] * 1536  # Mock vector
            mock_vector_service.return_value = mock_vector_instance
            
            # Mock Qdrant client
            mock_qdrant_instance = AsyncMock()
            mock_qdrant_instance.ensure_collection_exists.return_value = True
            mock_qdrant_instance.upsert_resume_point.return_value = True
            mock_qdrant_client.return_value = mock_qdrant_instance
            
            # Initialize service
            await service.initialize()
            
            # Process resume
            result = await service.process_resume_vector_only(
                email="<EMAIL>",
                resume_s3_url="https://skill-assessment-test.s3.amazonaws.com/test.pdf",
                filename="test.pdf"
            )
            
            # Verify result
            assert result.success
            assert result.email == "<EMAIL>"
            assert result.text_length > 0
            assert result.processing_time > 0
            
            # Verify service calls
            mock_pdf_instance.process_resume_pdf.assert_called_once()
            mock_vector_instance.generate_embedding_from_text.assert_called_once()
            mock_qdrant_instance.upsert_resume_point.assert_called_once()
            
            # Verify Qdrant upsert call structure
            call_args = mock_qdrant_instance.upsert_resume_point.call_args
            assert 'point_id' in call_args.kwargs
            assert 'vectors' in call_args.kwargs
            assert 'metadata' in call_args.kwargs
            
            # Verify vector structure
            vectors = call_args.kwargs['vectors']
            assert 'combined_vector' in vectors
            assert len(vectors['combined_vector']) == 1536
            
            # Verify metadata structure
            metadata = call_args.kwargs['metadata']
            assert metadata['email'] == "<EMAIL>"
            assert metadata['processing_approach'] == "vector_only"
            assert 'text_length' in metadata
            assert 'extraction_timestamp' in metadata


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
