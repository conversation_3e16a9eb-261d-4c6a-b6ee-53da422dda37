#!/usr/bin/env python3
"""
Test cases for the resume processing API endpoint.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import status

# Mock the dependencies before importing the app
with patch('services.api.clients.resume_service.process_resume') as mock_process_resume:
    from services.api.main import app

client = TestClient(app)


class TestResumeAPI:
    """Test cases for the resume processing API."""
    
    def test_resume_endpoint_exists(self):
        """Test that the resume endpoint exists and requires authentication."""
        response = client.post("/api/ingest/resume", json={
            "email": "<EMAIL>",
            "resume_s3_url": "https://example-bucket.s3.amazonaws.com/test.pdf"
        })
        
        # Should return 401 or 403 due to missing authentication
        assert response.status_code in [401, 403]
    
    @patch('services.api.clients.resume_service.process_resume')
    @patch('services.api.middleware.require_role')
    def test_resume_processing_success(self, mock_require_role, mock_process_resume):
        """Test successful resume processing."""
        # Mock authentication
        mock_require_role.return_value = lambda: {"sub": "test-user"}
        
        # Mock successful resume processing
        from services.api.clients.resume_service import ResumeProcessingResult
        from common.models import ResumeProfileData
        
        mock_profile_data = ResumeProfileData(
            full_name="John Doe",
            email="<EMAIL>",
            skills=["Python", "JavaScript"],
            work_experience=[],
            education=[]
        )
        
        mock_result = ResumeProcessingResult(
            success=True,
            email="<EMAIL>",
            profile_data=mock_profile_data,
            processing_time=2.5
        )
        
        mock_process_resume.return_value = mock_result
        
        # Make the request
        response = client.post("/api/ingest/resume", json={
            "email": "<EMAIL>",
            "resume_s3_url": "https://example-bucket.s3.amazonaws.com/test.pdf",
            "filename": "test-resume.pdf"
        })
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert "Resume processing completed successfully" in data["message"]
        assert "volunteer_id" in data
        
        # Verify the service was called correctly
        mock_process_resume.assert_called_once_with(
            email="<EMAIL>",
            resume_s3_url="https://example-bucket.s3.amazonaws.com/test.pdf",
            filename="test-resume.pdf"
        )
    
    @patch('services.api.clients.resume_service.process_resume')
    @patch('services.api.middleware.require_role')
    def test_resume_processing_failure(self, mock_require_role, mock_process_resume):
        """Test resume processing failure handling."""
        # Mock authentication
        mock_require_role.return_value = lambda: {"sub": "test-user"}
        
        # Mock failed resume processing
        from services.api.clients.resume_service import ResumeProcessingResult
        
        mock_result = ResumeProcessingResult(
            success=False,
            email="<EMAIL>",
            error_message="PDF extraction failed",
            error_type="extraction_error",
            processing_time=1.0
        )
        
        mock_process_resume.return_value = mock_result
        
        # Make the request
        response = client.post("/api/ingest/resume", json={
            "email": "<EMAIL>",
            "resume_s3_url": "https://invalid-url.com/test.pdf"
        })
        
        # Verify error response
        assert response.status_code == 422  # extraction_error maps to 422
        data = response.json()
        assert data["detail"]["error"] == "extraction_error"
        assert data["detail"]["message"] == "PDF extraction failed"
        assert data["detail"]["email"] == "<EMAIL>"
    
    @patch('services.api.middleware.require_role')
    def test_resume_invalid_email(self, mock_require_role):
        """Test validation of invalid email."""
        # Mock authentication
        mock_require_role.return_value = lambda: {"sub": "test-user"}
        
        # Make request with invalid email
        response = client.post("/api/ingest/resume", json={
            "email": "invalid-email",
            "resume_s3_url": "https://example-bucket.s3.amazonaws.com/test.pdf"
        })
        
        # Should return validation error
        assert response.status_code == 422
    
    @patch('services.api.middleware.require_role')
    def test_resume_invalid_url(self, mock_require_role):
        """Test validation of invalid S3 URL."""
        # Mock authentication
        mock_require_role.return_value = lambda: {"sub": "test-user"}
        
        # Make request with invalid URL
        response = client.post("/api/ingest/resume", json={
            "email": "<EMAIL>",
            "resume_s3_url": "not-a-url"
        })
        
        # Should return validation error
        assert response.status_code == 422
    
    @patch('services.api.clients.resume_service.process_resume')
    @patch('services.api.middleware.require_role')
    def test_resume_different_error_types(self, mock_require_role, mock_process_resume):
        """Test different error types map to correct HTTP status codes."""
        # Mock authentication
        mock_require_role.return_value = lambda: {"sub": "test-user"}
        
        from services.api.clients.resume_service import ResumeProcessingResult
        
        # Test different error types and their expected status codes
        error_test_cases = [
            ("invalid_url", 400),
            ("download_error", 400),
            ("file_too_large", 413),
            ("extraction_error", 422),
            ("vector_error", 500),
            ("storage_error", 500),
            ("unexpected_error", 500)
        ]
        
        for error_type, expected_status in error_test_cases:
            mock_result = ResumeProcessingResult(
                success=False,
                email="<EMAIL>",
                error_message=f"Test {error_type}",
                error_type=error_type,
                processing_time=1.0
            )
            
            mock_process_resume.return_value = mock_result
            
            response = client.post("/api/ingest/resume", json={
                "email": "<EMAIL>",
                "resume_s3_url": "https://example-bucket.s3.amazonaws.com/test.pdf"
            })
            
            assert response.status_code == expected_status, f"Error type {error_type} should return status {expected_status}"
            data = response.json()
            assert data["detail"]["error"] == error_type


@pytest.mark.asyncio
class TestResumeProcessingService:
    """Test cases for the resume processing service."""
    
    async def test_resume_service_initialization(self):
        """Test that the resume service initializes correctly."""
        from services.api.clients.resume_service import ResumeProcessingService
        
        service = ResumeProcessingService()
        
        # Should not be initialized yet
        assert not service._initialized
        
        # Mock the dependencies to avoid actual initialization
        with patch('services.api.clients.vector_service.VectorGenerationService') as mock_vector_service, \
             patch('services.api.clients.qdrant.get_qdrant_resumes_client') as mock_qdrant_client:
            
            mock_vector_instance = AsyncMock()
            mock_vector_service.return_value = mock_vector_instance
            
            mock_qdrant_instance = AsyncMock()
            mock_qdrant_instance.ensure_collection_exists.return_value = True
            mock_qdrant_client.return_value = mock_qdrant_instance
            
            await service.initialize()
            
            # Should be initialized now
            assert service._initialized
            mock_vector_instance.initialize.assert_called_once()
            mock_qdrant_instance.ensure_collection_exists.assert_called_once()


@pytest.mark.asyncio
class TestResumeParser:
    """Test cases for the resume parser."""
    
    async def test_personal_info_extraction(self):
        """Test extraction of personal information."""
        from services.api.clients.resume_parser import ResumeParser
        
        parser = ResumeParser()
        
        sample_text = """
        John Doe
        <EMAIL>
        (*************
        New York, NY 10001
        """
        
        info = parser.extract_personal_info(sample_text)
        
        assert info['full_name'] == "John Doe"
        assert info['email'] == "<EMAIL>"
        assert info['phone'] == "(*************"
    
    async def test_skills_parsing(self):
        """Test skills section parsing."""
        from services.api.clients.resume_parser import ResumeParser
        
        parser = ResumeParser()
        
        skills_text = "Python, JavaScript, React, Django, PostgreSQL, AWS, Docker"
        skills = parser.parse_skills_section(skills_text)
        
        expected_skills = ["Python", "JavaScript", "React", "Django", "PostgreSQL", "AWS", "Docker"]
        assert set(skills) == set(expected_skills)
    
    async def test_experience_parsing(self):
        """Test work experience parsing."""
        from services.api.clients.resume_parser import ResumeParser
        
        parser = ResumeParser()
        
        experience_text = """
        Senior Software Engineer at Tech Corp
        2020 - Present
        • Developed web applications using Python and React
        • Led a team of 3 developers
        
        Software Engineer at StartupXYZ
        2018 - 2020
        • Built REST APIs using Django
        """
        
        experiences = parser.parse_experience_section(experience_text)
        
        assert len(experiences) >= 1
        # Check that we extracted some job information
        assert any("Tech Corp" in exp.company_name or "Senior Software Engineer" in exp.job_title for exp in experiences)


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
